<!-- 编辑专家页面 -->
<template>
  <div class="expert-edit-page">
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>
    
    <van-form v-else @submit="handleSubmit" ref="formRef">
      <van-cell-group inset title="基本信息">
        <van-field
          v-model="formData.name"
          name="name"
          label="专家姓名"
          placeholder="请输入专家姓名"
          :rules="[{ required: true, message: '请输入专家姓名' }]"
        />
        
        <van-field
          v-model="genderText"
          is-link
          readonly
          name="gender"
          label="性别"
          placeholder="请选择性别"
          @click="showGenderPicker = true"
          :rules="[{ required: true, message: '请选择性别' }]"
        />
        
        <van-field
          v-model="formData.birthday"
          is-link
          readonly
          name="birthday"
          label="出生年月"
          placeholder="请选择出生年月"
          @click="showDatePicker = true"
          :rules="[{ required: true, message: '请选择出生年月' }]"
        />
        
        <van-field
          v-model="formData.idNum"
          name="idNum"
          label="身份证号"
          placeholder="请输入身份证号"
          :rules="[
            { required: true, message: '请输入身份证号' },
            { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号' }
          ]"
        />
        
        <van-field
          v-model="formData.phone"
          name="phone"
          label="联系电话"
          placeholder="请输入联系电话"
          :rules="[
            { required: true, message: '请输入联系电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]"
        />
      </van-cell-group>

      <van-cell-group inset title="专业信息">
        <van-field
          v-model="typeText"
          is-link
          readonly
          name="type"
          label="专家类型"
          placeholder="请选择专家类型"
          @click="showTypePicker = true"
          :rules="[{ required: true, message: '请选择专家类型' }]"
        />
        
        <van-field
          v-model="formData.post"
          name="post"
          label="单位职务"
          placeholder="请输入单位职务"
          :rules="[{ required: true, message: '请输入单位职务' }]"
        />
        
        <van-field
          v-model="formData.field"
          name="field"
          label="所在领域"
          placeholder="请输入所在领域"
          :rules="[{ required: true, message: '请输入所在领域' }]"
        />
        
        <van-field
          v-model="formData.level"
          name="level"
          label="专业级别"
          placeholder="请输入专业级别"
          :rules="[{ required: true, message: '请输入专业级别' }]"
        />
        
        <van-field
          v-model="serviceRangeText"
          is-link
          readonly
          name="serviceRange"
          label="服务范围"
          placeholder="请选择服务范围"
          @click="showServiceRangePicker = true"
          :rules="[{ required: true, message: '请选择服务范围' }]"
        />
      </van-cell-group>
      
      <van-cell-group inset title="状态设置">
        <van-field name="enabled" label="启用状态">
          <template #input>
            <van-switch v-model="formData.enabled" />
          </template>
        </van-field>
      </van-cell-group>
      
      <div class="submit-section">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="submitting"
        >
          {{ submitting ? '保存中...' : '保存' }}
        </van-button>
      </div>
    </van-form>

    <!-- 性别选择器 -->
    <van-popup v-model:show="showGenderPicker" position="bottom">
      <van-picker
        :columns="genderOptions"
        @confirm="onGenderConfirm"
        @cancel="showGenderPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </van-popup>

    <!-- 专家类型选择器 -->
    <van-popup v-model:show="showTypePicker" position="bottom">
      <van-picker
        :columns="typeOptions"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>

    <!-- 服务范围选择器 -->
    <van-popup v-model:show="showServiceRangePicker" position="bottom">
      <van-picker
        :columns="serviceRangeOptions"
        @confirm="onServiceRangeConfirm"
        @cancel="showServiceRangePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import { expertApi } from '@/api/expert'

const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const showGenderPicker = ref(false)
const showDatePicker = ref(false)
const showTypePicker = ref(false)
const showServiceRangePicker = ref(false)
const currentDate = ref(new Date())
const typeOptions = ref([])

const formData = reactive({
  name: '',
  gender: '',
  birthday: '',
  idNum: '',
  phone: '',
  typeId: '',
  post: '',
  field: '',
  level: '',
  serviceRange: '',
  enabled: true
})

// 选项数据
const genderOptions = [
  { text: '男', value: 'MALE' },
  { text: '女', value: 'FEMALE' }
]

const serviceRangeOptions = [
  { text: '全部', value: 'ALL' },
  { text: '局机关和直属单位', value: 'DIRECTLY' },
  { text: '区县', value: 'DISTRICT' }
]

const minDate = new Date(1950, 0, 1)
const maxDate = new Date()

// 计算属性
const genderText = computed(() => {
  const option = genderOptions.find(item => item.value === formData.gender)
  return option ? option.text : ''
})

const typeText = computed(() => {
  const option = typeOptions.value.find(item => item.value === formData.typeId)
  return option ? option.text : ''
})

const serviceRangeText = computed(() => {
  const option = serviceRangeOptions.find(item => item.value === formData.serviceRange)
  return option ? option.text : ''
})

// 方法
const fetchExpertDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    const response = await expertApi.getExpertDetail(id)
    
    // 填充表单数据
    Object.assign(formData, {
      name: response.name || '',
      gender: response.gender || '',
      birthday: response.birthday || '',
      idNum: response.idNum || '',
      phone: response.phone || '',
      typeId: response.type?.id || '',
      post: response.post || '',
      field: response.field || '',
      level: response.level || '',
      serviceRange: response.serviceRange || '',
      enabled: response.enabled !== false
    })
    
    // 设置日期选择器的当前值
    if (response.birthday) {
      currentDate.value = new Date(response.birthday)
    }
    
  } catch (error) {
    console.error('获取专家详情失败:', error)
    showToast('获取详情失败')
  } finally {
    loading.value = false
  }
}

const fetchExpertTypes = async () => {
  try {
    const response = await expertApi.getExpertCategories()
    typeOptions.value = response.list?.map(item => ({
      text: item.name,
      value: item.id
    })) || []
  } catch (error) {
    console.error('获取专家类型失败:', error)
    // 使用模拟数据
    typeOptions.value = [
      { text: '农业测试类', value: '1' },
      { text: '财务审计类', value: '2' }
    ]
  }
}

const onGenderConfirm = ({ selectedOptions }) => {
  formData.gender = selectedOptions[0].value
  showGenderPicker.value = false
}

const onDateConfirm = (value) => {
  const year = value.getFullYear()
  const month = String(value.getMonth() + 1).padStart(2, '0')
  const day = String(value.getDate()).padStart(2, '0')
  formData.birthday = `${year}-${month}-${day}`
  showDatePicker.value = false
}

const onTypeConfirm = ({ selectedOptions }) => {
  formData.typeId = selectedOptions[0].value
  showTypePicker.value = false
}

const onServiceRangeConfirm = ({ selectedOptions }) => {
  formData.serviceRange = selectedOptions[0].value
  showServiceRangePicker.value = false
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    const submitData = {
      name: formData.name.trim(),
      gender: formData.gender,
      birthday: formData.birthday,
      idNum: formData.idNum.trim(),
      phone: formData.phone.trim(),
      typeId: formData.typeId,
      post: formData.post.trim(),
      field: formData.field.trim(),
      level: formData.level.trim(),
      serviceRange: formData.serviceRange,
      enabled: formData.enabled
    }
    
    await expertApi.updateExpert(route.params.id, submitData)
    
    showSuccessToast('专家更新成功')
    router.back()
    
  } catch (error) {
    console.error('更新专家失败:', error)
    showToast('更新失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchExpertTypes()
  fetchExpertDetail()
})

onActivated(() => {
  fetchExpertDetail()
})
</script>

<style lang="scss" scoped>
.expert-edit-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 100px;
  flex-shrink: 0;
}

:deep(.van-field__control) {
  text-align: right;
}
</style>

<route lang="json5">
{
  name: 'ExpertEdit',
  meta: {
    title: '编辑专家'
  }
}
</route>
