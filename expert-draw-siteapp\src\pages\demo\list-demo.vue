<!-- FuniList 组件使用示例 -->
<template>
  <div class="list-demo-page">
    <van-nav-bar title="列表组件示例" left-arrow @click-left="$router.back()" />
    
    <!-- 专家列表示例 -->
    <FuniList
      ref="expertListRef"
      :load-function="loadExpertList"
      :filter-config="expertFilterConfig"
      search-placeholder="搜索专家姓名"
      item-key="id"
      @item-click="handleExpertClick"
      @filter-change="handleFilterChange"
      @search-change="handleSearchChange"
    >
      <!-- 自定义头部 -->
      <template #header>
        <div class="demo-header">
          <van-notice-bar text="这是专家列表示例，支持搜索和筛选功能" />
        </div>
      </template>

      <!-- 自定义列表项 -->
      <template #item="{ item, index }">
        <div class="expert-item">
          <!-- 专家头像和基本信息 -->
          <div class="expert-header">
            <div class="expert-avatar">
              <van-icon name="contact" />
            </div>
            <div class="expert-basic">
              <div class="expert-name">{{ item.name }}</div>
              <div class="expert-info">
                <span class="gender">{{ getGenderText(item.gender) }}</span>
                <span class="birthday">{{ item.birthday }}</span>
                <span class="id-num">{{ item.idNum }}</span>
              </div>
            </div>
          </div>

          <!-- 专家详细信息 -->
          <div class="expert-details">
            <div class="detail-row">
              <div class="detail-item">
                <span class="label">专家类型</span>
                <span class="value">{{ item.type?.name }}</span>
              </div>
              <div class="detail-item">
                <span class="label">单位职务</span>
                <span class="value">{{ item.post }}</span>
              </div>
            </div>
            
            <div class="detail-row">
              <div class="detail-item">
                <span class="label">所在领域</span>
                <span class="value">{{ item.field }}</span>
              </div>
              <div class="detail-item">
                <span class="label">专业级别</span>
                <span class="value">{{ item.level }}</span>
              </div>
            </div>
            
            <div class="detail-row">
              <div class="detail-item">
                <span class="label">手机号</span>
                <span class="value">{{ item.phone }}</span>
              </div>
              <div class="detail-item">
                <span class="label">座机号</span>
                <span class="value">{{ item.landline }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 自定义底部 -->
      <template #footer>
        <div class="demo-footer">
          <van-button type="primary" block @click="refreshList">刷新列表</van-button>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import FuniList from '@/components/FuniList.vue'
import useExpert from '@/hook/expert.js'

const router = useRouter()
const { getExpertList, expertTypes } = useExpert()

const expertListRef = ref()

// 专家筛选配置
const expertFilterConfig = computed(() => [
  {
    type: 'input',
    label: '姓名',
    prop: 'name',
    props: {
      placeholder: '请输入专家姓名',
      clearable: true
    }
  },
  {
    type: 'select',
    label: '类型',
    prop: 'typeId',
    options: expertTypes.value,
    props: {
      placeholder: '请选择专家类型'
    }
  },
  {
    type: 'select',
    label: '性别',
    prop: 'gender',
    options: window.$enums?.getEnumOptions?.('Gender', true) || [
      { text: '全部', value: '' },
      { text: '男', value: 'MALE' },
      { text: '女', value: 'FEMALE' }
    ],
    props: {
      placeholder: '请选择性别'
    }
  },
  {
    type: 'input',
    label: '身份证号',
    prop: 'idNum',
    props: {
      placeholder: '请输入身份证号',
      clearable: true
    }
  },
  {
    type: 'input',
    label: '手机号',
    prop: 'phone',
    props: {
      placeholder: '请输入手机号',
      clearable: true
    }
  }
])

// 加载专家列表数据
const loadExpertList = async (params) => {
  try {
    console.log('加载专家列表参数:', params)
    
    // 调用专家列表API
    const result = await getExpertList(params)
    
    // 返回标准格式的数据
    return {
      data: result.data || [],
      total: result.total || 0
    }
  } catch (error) {
    console.error('加载专家列表失败:', error)
    throw error
  }
}

// 获取性别文本
const getGenderText = (gender) => {
  return window.$enums?.getEnumDesc?.('Gender', gender) || gender
}

// 处理专家点击
const handleExpertClick = (expert, index) => {
  console.log('点击专家:', expert, index)
  showToast(`点击了专家: ${expert.name}`)
  
  // 跳转到专家详情页
  if (expert.id) {
    router.push({
      path: '/pub/expert/detail',
      query: { id: expert.id }
    })
  }
}

// 处理筛选变化
const handleFilterChange = (filterData) => {
  console.log('筛选条件变化:', filterData)
  showToast('筛选条件已更新')
}

// 处理搜索变化
const handleSearchChange = (keyword) => {
  console.log('搜索关键词变化:', keyword)
}

// 刷新列表
const refreshList = () => {
  if (expertListRef.value) {
    expertListRef.value.refresh()
    showToast('列表已刷新')
  }
}
</script>

<style lang="scss" scoped>
.list-demo-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.demo-header {
  padding: 8px 16px;
  background: white;
}

.demo-footer {
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.expert-item {
  .expert-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .expert-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: #f7f8fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      .van-icon {
        font-size: 24px;
        color: #646566;
      }
    }

    .expert-basic {
      flex: 1;

      .expert-name {
        font-size: 16px;
        font-weight: 600;
        color: #323233;
        margin-bottom: 4px;
      }

      .expert-info {
        display: flex;
        gap: 12px;
        font-size: 12px;
        color: #646566;

        span {
          &:not(:last-child)::after {
            content: '|';
            margin-left: 12px;
            color: #ddd;
          }
        }
      }
    }
  }

  .expert-details {
    .detail-row {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .detail-item {
      flex: 1;

      &:first-child {
        margin-right: 16px;
      }

      .label {
        font-size: 12px;
        color: #646566;
        margin-bottom: 2px;
        display: block;
      }

      .value {
        font-size: 14px;
        color: #323233;
      }
    }
  }
}
</style>

<route lang="json5">
{
  name: 'ListDemo',
  meta: {
    title: '列表组件示例'
  }
}
</route>
