<!-- 专家库管理页面 -->
<template>
  <div class="expert-management-page">
    <!-- 头部绿色渐变区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-line">专家库管理</div>
      </div>
      <div class="header-bg-pattern">
        <img src="@/assets/bg.png" alt="背景" class="bg-image" />
      </div>
    </div>

    <!-- 功能模块区域 -->
    <div class="function-modules">
      <div class="module-list">
        <!-- 专家种类列表 -->
        <div class="module-card" @click="handleExpertCategories">
          <div class="module-icon">
            <van-icon name="apps-o" size="24" color="white" />
          </div>
          <div class="module-content">
            <div class="module-title">专家种类列表</div>
            <div class="module-desc">管理专家分类和种类</div>
          </div>
          <van-icon name="arrow" class="arrow-icon" />
        </div>

        <!-- 专家资料库 -->
        <div class="module-card" @click="handleExpertDatabase">
          <div class="module-icon expert-icon">
            <van-icon name="contact" size="24" color="white" />
          </div>
          <div class="module-content">
            <div class="module-title">专家资料库</div>
            <div class="module-desc">查看和管理专家信息</div>
          </div>
          <van-icon name="arrow" class="arrow-icon" />
        </div>
      </div>
    </div>

    <!-- 统计信息区域 -->
    <div class="stats-section" v-if="!loading">
      <van-cell-group>
        <van-cell title="专家种类数量" :value="expertStats.categoryCount || 0" />
        <van-cell title="专家总数" :value="expertStats.expertCount || 0" />
        <van-cell title="启用专家数" :value="expertStats.enabledCount || 0" />
        <van-cell title="今日新增专家" :value="expertStats.todayAddCount || 0" />
      </van-cell-group>
    </div>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>

    <!-- 底部信息 -->
    <div class="footer-info">
      <div class="contact-info">
        <div>专家库管理系统</div>
        <div>统一管理专家信息和分类</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { expertApi } from '@/api/expert'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const expertStats = ref({})

// 方法
const fetchExpertStats = async () => {
  try {
    loading.value = true
    // 这里可以调用获取专家统计数据的API
    // const stats = await expertApi.getExpertStats()
    // expertStats.value = stats
    
    // 使用模拟数据
    expertStats.value = {
      categoryCount: 8,
      expertCount: 156,
      enabledCount: 142,
      todayAddCount: 3
    }
  } catch (error) {
    console.error('获取专家统计数据失败:', error)
    showToast('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

const handleExpertCategories = () => {
  router.push('/gov/expert/categories')
}

const handleExpertDatabase = () => {
  router.push('/gov/expert/list')
}

// 生命周期
onMounted(() => {
  fetchExpertStats()
})

onActivated(() => {
  fetchExpertStats()
})
</script>

<style lang="scss" scoped>
.expert-management-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.header-section {
  position: relative;
  height: 160px;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.header-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.title-line {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.2;
}

.header-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;

  .bg-image {
    position: absolute;
    right: -20px;
    bottom: -20px;
    width: 150px;
    height: 150px;
    opacity: 0.3;
    object-fit: contain;
  }
}

.function-modules {
  padding: 24px 16px;
  background: white;
  margin-top: -20px;
  border-radius: 20px 20px 0 0;
  position: relative;
  z-index: 3;
}

.module-list {
  .module-card {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 20px 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:active {
      transform: scale(0.98);
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.12);
    }
  }
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: linear-gradient(135deg, #2196F3, #1565C0);

  &.expert-icon {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
  }
}

.module-content {
  flex: 1;
}

.module-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  margin-bottom: 4px;
}

.module-desc {
  font-size: 14px;
  color: var(--van-text-color-2);
}

.arrow-icon {
  color: var(--van-text-color-3);
}

.stats-section {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.footer-info {
  margin-top: 40px;
  padding: 20px;
  text-align: center;
}

.contact-info {
  font-size: 12px;
  color: var(--van-text-color-2);
  line-height: 1.5;
}
</style>

<route lang="json5">
{
  name: 'ExpertManagement',
  meta: {
    title: '专家库管理'
  }
}
</route>
