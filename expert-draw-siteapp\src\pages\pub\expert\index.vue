<!-- 专家资料库列表页面 -->
<template>
  <div class="expert-list-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索专家姓名"
        @search="onSearch"
        @clear="onClear"
      />
      <div class="filter-icon" @click="showFilterPopup = true">
        <van-icon name="wap-nav" />
        <span>筛选</span>
      </div>
    </div>

    <!-- 专家列表 -->
    <div class="expert-list">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="expertList.length ?'没有更多了':''"
          @load="loadExpertList"
        >
          <div
            v-for="item in expertList"
            :key="item.id"
            class="expert-item"
            @click="goToDetail(item.id)"
          >
            <!-- 专家头像和基本信息 -->
            <div class="expert-header">
              <div class="expert-avatar">
                <van-icon name="contact" />
              </div>
              <div class="expert-basic">
                <div class="expert-name">{{ item.name }}</div>
                <div class="expert-info">
                  <span class="gender">{{ getGenderText(item.gender) }}</span>
                  <span class="birthday">{{ item.birthday}}</span>
                  <span class="id-num">{{ item.idNum }}</span>
                </div>
              </div>
            </div>

            <!-- 专家详细信息 -->
            <div class="expert-details">
              <div class="detail-row">
                <div class="detail-item">
                  <span class="label">专家类型</span>
                  <span class="value">{{ item.type?.name  }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">单位职务</span>
                  <span class="value">{{ item.post }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="label">所在领域</span>
                  <span class="value">{{ item.field}}</span>
                </div>
                <div class="detail-item">
                  <span class="label">专业级别(技术职称)</span>
                  <span class="value">{{ item.level}}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="label">手机号</span>
                  <span class="value">{{ item.phone}}</span>
                </div>
                <div class="detail-item">
                  <span class="label">座机号</span>
                  <span class="value">{{ item.landline}}</span>
                </div>
              </div>
            </div>
          </div>
          <van-empty v-if="!expertList.length && !loading" description="暂无数据" />
        </van-list>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup
      v-model:show="showFilterPopup"
      position="bottom"
      round
      :style="{ height: '60%' }"
    >
      <div class="filter-content">
        <div class="filter-header">
          <span class="filter-title">筛选条件</span>
          <van-icon name="cross" @click="showFilterPopup = false" class="close-icon" />
        </div>

        <div class="filter-body">
          <FuniForm
            ref="FuniFormRef"
            v-model="formData"
            :form-config="formConfig"
            @submit="formSubmit"
          />
        </div>

        <!-- 筛选按钮 -->
        <div class="filter-actions">
          <van-button @click="btnReset" class="reset-btn" size="small">重置</van-button>
          <van-button type="primary" @click="btnSubmit" class="apply-btn" size="small">确定</van-button>
        </div>
      </div>
    </van-popup>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import useExpert from '@/hook/expert.js'

const router = useRouter()
const { getExpertList,expertTypes } = useExpert()

const FuniFormRef = ref()

// 响应式数据
const searchKeyword = ref('')
const loading = ref(false)
const finished = ref(false)
const showFilterPopup = ref(false)
const expertList = ref([])

//筛选表单
let formData =ref({})

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 20
})

// 筛选表单配置
const formConfig = computed(()=>{
  return [
  {
    type: 'select',
    label: '类型',
    prop: 'typeId',
    options: expertTypes.value,
    props: {
      placeholder: '请选择专家类型'
    }
  },
  {
    type: 'select',
    label: '性别',
    prop: 'gender',
    options: window.$enums.getEnums("Gender"),
    props: {
      placeholder: '请选择性别'
    }
  },
  {
    type: 'input',
    label: '身份证号',
    prop: 'idNum',
    props: {
      placeholder: '请输入身份证号',
      clearable: true
    }
  },
  {
    type: 'input',
    label: '手机号',
    prop: 'phone',
    props: {
      placeholder: '请输入手机号',
      clearable: true
    }
  }
]
})

/**
 * 表单提交事件
 */
function formSubmit(values){
  showFilterPopup.value = false
  onSearch()
}
// 重置
const btnReset = () => {
  formData.value = {}
}
/**
 * 提交
 */
function btnSubmit(){
  FuniFormRef.value.submit()
}

// 重置列表
const resetList = () => {
  expertList.value = []
  pagination.page = 1
  finished.value = false
  loading.value = false
}

// 搜索
const onSearch = () => {
  resetList()
  loadExpertList()
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  onSearch()
}


// 加载专家列表
const loadExpertList = async () => {
    loading.value = true
    const params = {
      page:  pagination.page,
      pageSize: pagination.pageSize,
      name: searchKeyword.value,
      ...(formData.value || {})
    }
    
    // 调用API
    const res = await getExpertList(params)
    expertList.value.push(...res.data)
    pagination.page++
    // 判断是否还有更多数据
    if (expertList.value.length >= res.total) {
      finished.value = true
    }
   loading.value = false
}

// 获取性别文本
const getGenderText = (gender) => {
  return  window.$enums.getEnumText('Gender',gender)
}


// 跳转到专家详情页
const goToDetail = (expertId) => {
  if (!expertId) {
    showToast('专家ID不能为空')
    return
  }
  router.push({path:`/pub/expert/detail`,query:{id:expertId}})
}

// 页面初始化
onMounted(() => {
  loadExpertList()
})
</script>

<style lang="scss" scoped>
.expert-list-page {
  height: calc(100vh - 46px);
  display: flex;
  flex-direction: column;
}

.search-section {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;

  :deep(.van-search) {
    flex: 1;
    padding: 0;
    background: #f7f8fa;

    .van-search__content {
      background: #f7f8fa;
      border-radius: 20px;
    }

    .van-field__control {
      font-size: 14px;
    }
  }

  .filter-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 12px;
    padding: 8px;
    cursor: pointer;

    .van-icon {
      font-size: 18px;
      color: #646566;
      margin-bottom: 2px;
    }

    span {
      font-size: 12px;
      color: #646566;
    }
  }
}

.expert-list {
  padding: 12px 16px;
  flex:1;
  overflow: scroll;
}

.expert-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
}

.expert-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .expert-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    .van-icon {
      font-size: 24px;
      color: #969799;
    }
  }

  .expert-basic {
    flex: 1;

    .expert-name {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 4px;
    }

    .expert-info {
      font-size: 12px;
      color: #646566;

      span {
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.expert-details {
  .detail-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .detail-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .value {
        font-size: 14px;
        color: #000;
        font-weight: 500;
        &:empty::after {
          content:"--"
        }
      }
    }
  }
}

.filter-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 16px 16px;
    border-bottom: 1px solid #f0f0f0;
    position: relative;

    .filter-title {
      font-size: 18px;
      font-weight: 600;
      color: #323233;
      text-align: center;
      flex: 1;
    }

    .close-icon {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 20px;
      color: #969799;
      cursor: pointer;
    }
  }

  .filter-body {
    flex: 1;
    padding: 0;
    overflow-y: auto;

    .van-form {
      padding: 0;
    }

    .van-cell {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }

    .van-field__label {
      width: 80px;
      color: #323233;
      font-size: 14px;
    }

    .van-field__control {
      color: #323233;
      font-size: 14px;
    }
  }
}



.filter-actions {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: white;

  .reset-btn {
    flex: 1;
    height: 48px;
    background: #f7f8fa;
    color: #646566;
    border: 1px solid #ebedf0;
    border-radius: 24px;
    font-size: 16px;
  }

  .apply-btn {
    flex: 2;
    height: 48px;
    background: #07c160;
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 500;
  }
}

</style>
<route lang="json5">
{
  name: "expert",
  meta: {
    title: "专家资料库",
  },
}
</route>
