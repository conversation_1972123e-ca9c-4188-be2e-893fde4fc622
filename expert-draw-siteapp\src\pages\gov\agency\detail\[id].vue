<!-- 代理机构详情页面 -->
<template>
  <div class="agency-detail-page">
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>
    
    <div v-else-if="agencyDetail" class="detail-content">
      <!-- 基本信息 -->
      <van-cell-group inset title="基本信息">
        <van-cell title="机构名称" :value="agencyDetail.name" />
        <van-cell title="状态">
          <template #value>
            <van-tag :type="agencyDetail.enabled ? 'success' : 'default'">
              {{ agencyDetail.enabled ? '启用' : '禁用' }}
            </van-tag>
          </template>
        </van-cell>
        <van-cell title="服务范围" :value="getServiceRangeText(agencyDetail.serviceRange)" />
        <van-cell title="创建时间" :value="formatDate(agencyDetail.createTime)" />
      </van-cell-group>

      <!-- 负责人信息 -->
      <van-cell-group inset title="负责人信息">
        <van-cell title="负责人" :value="agencyDetail.leader" />
        <van-cell title="联系电话">
          <template #value>
            <a :href="`tel:${agencyDetail.leaderPhone}`" class="phone-link">
              {{ agencyDetail.leaderPhone }}
            </a>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 经办人信息 -->
      <van-cell-group inset title="经办人信息">
        <van-cell title="经办人1" :value="agencyDetail.contact" />
        <van-cell title="联系电话">
          <template #value>
            <a :href="`tel:${agencyDetail.phone}`" class="phone-link">
              {{ agencyDetail.phone }}
            </a>
          </template>
        </van-cell>
        
        <van-cell 
          v-if="agencyDetail.contactb" 
          title="经办人2" 
          :value="agencyDetail.contactb" 
        />
        <van-cell 
          v-if="agencyDetail.phoneb" 
          title="联系电话"
        >
          <template #value>
            <a :href="`tel:${agencyDetail.phoneb}`" class="phone-link">
              {{ agencyDetail.phoneb }}
            </a>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-section">
        <van-button
          type="primary"
          size="large"
          block
          @click="handleEdit"
        >
          编辑
        </van-button>
        
        <van-button
          :type="agencyDetail.enabled ? 'warning' : 'success'"
          size="large"
          block
          @click="handleToggleStatus"
          :loading="statusLoading"
        >
          {{ agencyDetail.enabled ? '禁用' : '启用' }}
        </van-button>
        
        <van-button
          type="danger"
          size="large"
          block
          @click="handleDelete"
          :loading="deleteLoading"
        >
          删除
        </van-button>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-else
      description="代理机构不存在"
      image="error"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { agencyApi } from '@/api/agency'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const statusLoading = ref(false)
const deleteLoading = ref(false)
const agencyDetail = ref(null)

// 服务范围映射
const serviceRangeMap = {
  'ALL': '全部',
  'DIRECTLY': '局机关和直属单位',
  'DISTRICT': '区县'
}

// 方法
const fetchAgencyDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    const response = await agencyApi.getAgencyDetail(id)
    agencyDetail.value = response
  } catch (error) {
    console.error('获取代理机构详情失败:', error)
    showToast('获取详情失败')
    
    // 使用模拟数据
    agencyDetail.value = {
      id: route.params.id,
      name: '四川鲁通天和招标代理有限公司',
      contact: '张三',
      phone: '18712345382',
      contactb: '李四',
      phoneb: '18712345382',
      leader: '张三',
      leaderPhone: '18712345382',
      enabled: true,
      serviceRange: 'DIRECTLY',
      createTime: '2024-01-15T10:30:00'
    }
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push(`/gov/agency/edit/${route.params.id}`)
}

const handleToggleStatus = async () => {
  try {
    const action = agencyDetail.value.enabled ? '禁用' : '启用'
    await showConfirmDialog({
      title: '确认操作',
      message: `确定要${action}该代理机构吗？`
    })
    
    statusLoading.value = true
    const newStatus = !agencyDetail.value.enabled
    
    await agencyApi.toggleAgencyStatus(route.params.id, newStatus)
    
    agencyDetail.value.enabled = newStatus
    showSuccessToast(`${action}成功`)
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换状态失败:', error)
      showToast('操作失败，请重试')
    }
  } finally {
    statusLoading.value = false
  }
}

const handleDelete = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '删除后无法恢复，确定要删除该代理机构吗？'
    })
    
    deleteLoading.value = true
    await agencyApi.deleteAgency(route.params.id)
    
    showSuccessToast('删除成功')
    router.back()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      showToast('删除失败，请重试')
    }
  } finally {
    deleteLoading.value = false
  }
}

const getServiceRangeText = (range) => {
  return serviceRangeMap[range] || range
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  fetchAgencyDetail()
})

onActivated(() => {
  fetchAgencyDetail()
})
</script>

<style lang="scss" scoped>
.agency-detail-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  .van-cell-group {
    margin-bottom: 16px;
  }
}

.phone-link {
  color: var(--van-primary-color);
  text-decoration: none;
}

.action-section {
  margin-top: 32px;
  
  .van-button {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

<route lang="json5">
{
  name: 'AgencyDetail',
  meta: {
    title: '代理机构详情'
  }
}
</route>
