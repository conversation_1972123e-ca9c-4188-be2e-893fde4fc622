<!-- 代理机构列表页面 -->
<template>
  <div class="agency-list-page">
    <FuniList
      ref="agencyListRef"
      :tabs="tabsConfig"
      item-key="id"
      empty-text="暂无代理机构数据"
      @item-click="handleAgencyClick"
    >
      <template #item="{ item: agency }">
        <div class="agency-card">
          <div class="agency-header">
            <div class="agency-name">{{ agency.name }}</div>
          </div>

          <div class="agency-info">
            <div class="info-row">
              <span class="label">负责人</span>
            </div>
             <div class="info-row">
              <span class="value">{{ agency.leader }} {{ agency.leaderPhone }}</span>
            </div>
            <div class="info-row">
              <span class="label">经办人</span>
            </div>
             <div class="info-row">
              <span class="value">{{ agency.contact }} {{ agency.phone }}  {{ agency.contactb }} {{ agency.phoneb }}</span>
            </div>
            <div class="info-row">
              <span class="label">服务范围</span>
            </div>
            <div class="info-row">
              <span class="value">{{ getServiceRangeText(agency.serviceRange) }}</span>
            </div>
          </div>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { agencyApi } from '@/api/agency'
import FuniList from '@/components/FuniList.vue'

const router = useRouter()
const agencyListRef = ref()


// 数据加载函数
const loadAgencyData = async (params) => {
    const response = await agencyApi.getAgencyList(params)
    // 处理返回数据
    return {
      data: response.data || response.list || [],
      total: response.total || response.count || 0
    }
}

// Tabs 配置
const tabsConfig = [
  {
    key: 'agencies',
    title: '代理机构',
    loadFunction: loadAgencyData,
    searchPlaceholder: '模糊检索(名称，联系人电话)',
    keyword: 'searchText',
    filterConfig: [
      {
        type: 'select',
        label: '服务范围',
        prop: 'serviceRange',
        options:  window.$enums.getEnums("ServiceRange"),
      }
    ]
  }
]

// 事件处理方法
const handleAgencyClick = (agency, index) => {
  console.log('点击代理机构:', agency, index)
  // router.push(`/gov/agency/detail/${agency.id}`)
}

const getServiceRangeText = (range) => {
  return window.$enums.getEnumText("ServiceRange",range)
}
</script>

<style lang="scss" scoped>
.agency-list-page {
  height: calc(100vh - 46px);
  background-color: var(--van-background);
}

.agency-card {

}

.agency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.agency-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  flex: 1;
  margin-right: 12px;
}

.agency-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: var(--van-gray-3);
  color: var(--van-text-color-2);

  &.active {
    background-color: var(--van-success-color);
    color: white;
  }
}

.agency-info {
  .info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .label {
    color: var(--van-text-color-2);
    width: 80px;
    flex-shrink: 0;
  }

  .value {
    color: var(--van-text-color);
    flex: 1;
  }
}
</style>

<route lang="json5">
{
  name: 'AgencyList',
  meta: {
    title: '代理机构列表'
  }
}
</route>
