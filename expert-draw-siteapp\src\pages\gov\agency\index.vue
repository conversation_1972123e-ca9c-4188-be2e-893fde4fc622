<!-- 代理机构列表页面 -->
<template>
  <div class="agency-list-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索机构名称、联系人、电话"
        @search="handleSearch"
        @clear="handleClear"
        show-action
      >
        <template #action>
          <div @click="handleSearch">搜索</div>
        </template>
      </van-search>
    </div>

    <!-- 代理机构列表 -->
    <div class="agency-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="agency in agencyList"
            :key="agency.id"
            class="agency-card"
            @click="handleAgencyDetail(agency)"
          >
            <div class="agency-header">
              <div class="agency-name">{{ agency.name }}</div>
              <div class="agency-status" :class="{ active: agency.enabled }">
                {{ agency.enabled ? '启用' : '禁用' }}
              </div>
            </div>
            
            <div class="agency-info">
              <div class="info-row">
                <span class="label">负责人</span>
                <span class="value">{{ agency.leader }}</span>
              </div>
              <div class="info-row">
                <span class="label">电话</span>
                <span class="value">{{ agency.leaderPhone }}</span>
              </div>
              <div class="info-row">
                <span class="label">经办人</span>
                <span class="value">{{ agency.contact }} {{ agency.phone }}</span>
              </div>
              <div class="info-row">
                <span class="label">服务范围</span>
                <span class="value">{{ getServiceRangeText(agency.serviceRange) }}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && agencyList.length === 0"
      description="暂无代理机构数据"
      image="search"
    />

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAddAgency"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { agencyApi } from '@/api/agency'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const agencyList = ref([])
const currentPage = ref(1)
const pageSize = ref(20)

// 服务范围映射
const serviceRangeMap = {
  'ALL': '全部',
  'DIRECTLY': '局机关和直属单位',
  'DISTRICT': '区县'
}

// 方法
const fetchAgencyList = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1
      finished.value = false
    }
    
    loading.value = true
    const params = {
      pageSize: pageSize.value,
      page: currentPage.value
    }
    
    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }
    
    const response = await agencyApi.getAgencyList(params)
    
    if (isRefresh) {
      agencyList.value = response.data
    } else {
      agencyList.value.push(...(response.list || response || []))
    }
    
    // 判断是否还有更多数据
    if (!response.list || response.list.length < pageSize.value) {
      finished.value = true
    }
    
  } catch (error) {
    console.error('获取代理机构列表失败:', error)
    showToast('获取数据失败')
    
    // 使用模拟数据
    if (isRefresh || agencyList.value.length === 0) {
      agencyList.value = [
        {
          id: '1',
          name: '四川鲁通天和招标代理有限公司',
          contact: '张三',
          phone: '187****3382',
          contactb: '李四',
          phoneb: '187****3382',
          leader: '张三',
          leaderPhone: '187****3382',
          enabled: true,
          serviceRange: 'DIRECTLY',
          createTime: '2024-01-15'
        },
        {
          id: '2',
          name: '四川鲁通天和招标代理有限公司',
          contact: '张三',
          phone: '187****3382',
          contactb: '李四',
          phoneb: '187****3382',
          leader: '张三',
          leaderPhone: '187****3382',
          enabled: true,
          serviceRange: 'DIRECTLY',
          createTime: '2024-01-15'
        },
        {
          id: '3',
          name: '四川鲁通天和招标代理有限公司',
          contact: '张三',
          phone: '187****3382',
          contactb: '李四',
          phoneb: '187****3382',
          leader: '张三',
          leaderPhone: '187****3382',
          enabled: true,
          serviceRange: 'DIRECTLY',
          createTime: '2024-01-15'
        }
      ]
      finished.value = true
    }
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onLoad = () => {
  if (!finished.value) {
    currentPage.value++
    fetchAgencyList()
  }
}

const onRefresh = () => {
  fetchAgencyList(true)
}

const handleSearch = () => {
  fetchAgencyList(true)
}

const handleClear = () => {
  searchKeyword.value = ''
  fetchAgencyList(true)
}

const handleAgencyDetail = (agency) => {
  router.push(`/gov/agency/detail/${agency.id}`)
}

const handleAddAgency = () => {
  router.push('/gov/agency/add')
}

const getServiceRangeText = (range) => {
  return serviceRangeMap[range] || range
}

// 生命周期
onMounted(() => {
  fetchAgencyList(true)
})

onActivated(() => {
  fetchAgencyList(true)
})
</script>

<style lang="scss" scoped>
.agency-list-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.search-section {
  background: white;
  padding: 8px 0;
  border-bottom: 1px solid var(--van-border-color);
}

.agency-list {
  padding: 8px 16px;
}

.agency-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.agency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.agency-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  flex: 1;
  margin-right: 12px;
}

.agency-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: var(--van-gray-3);
  color: var(--van-text-color-2);

  &.active {
    background-color: var(--van-success-color);
    color: white;
  }
}

.agency-info {
  .info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .label {
    color: var(--van-text-color-2);
    width: 80px;
    flex-shrink: 0;
  }

  .value {
    color: var(--van-text-color);
    flex: 1;
  }
}
</style>

<route lang="json5">
{
  name: 'AgencyList',
  meta: {
    title: '代理机构列表'
  }
}
</route>
