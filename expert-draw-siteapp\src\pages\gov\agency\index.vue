<!-- 代理机构列表页面 -->
<template>
  <div class="agency-list-page">
    <FuniList
      ref="agencyListRef"
      :load-function="loadAgencyData"
      :filter-config="filterConfig"
      search-placeholder="搜索机构名称、联系人、电话"
      item-key="id"
      empty-text="暂无代理机构数据"
      @item-click="handleAgencyClick"
    >
      <template #item="{ item: agency, index }">
        <div class="agency-card">
          <div class="agency-header">
            <div class="agency-name">{{ agency.name }}</div>
            <div class="agency-status" :class="{ active: agency.enabled }">
              {{ agency.enabled ? '启用' : '禁用' }}
            </div>
          </div>

          <div class="agency-info">
            <div class="info-row">
              <span class="label">负责人</span>
              <span class="value">{{ agency.leader }}</span>
            </div>
            <div class="info-row">
              <span class="label">电话</span>
              <span class="value">{{ agency.leaderPhone }}</span>
            </div>
            <div class="info-row">
              <span class="label">经办人</span>
              <span class="value">{{ agency.contact }} {{ agency.phone }}</span>
            </div>
            <div class="info-row">
              <span class="label">服务范围</span>
              <span class="value">{{ getServiceRangeText(agency.serviceRange) }}</span>
            </div>
          </div>
        </div>
      </template>

      <template #footer>
        <!-- 浮动添加按钮 -->
        <van-floating-bubble
          axis="xy"
          icon="plus"
          @click="handleAddAgency"
        />
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { agencyApi } from '@/api/agency'
import FuniList from '@/components/FuniList.vue'

const router = useRouter()
const agencyListRef = ref()

// 服务范围映射
const serviceRangeMap = {
  'ALL': '全部',
  'DIRECTLY': '局机关和直属单位',
  'DISTRICT': '区县'
}

// 筛选配置
const filterConfig = [
  {
    type: 'select',
    label: '状态',
    prop: 'enabled',
    options: [
      { text: '全部', value: '' },
      { text: '启用', value: true },
      { text: '禁用', value: false }
    ],
    props: {
      placeholder: '请选择状态'
    }
  },
  {
    type: 'select',
    label: '服务范围',
    prop: 'serviceRange',
    options: [
      { text: '全部', value: '' },
      { text: '全部', value: 'ALL' },
      { text: '局机关和直属单位', value: 'DIRECTLY' },
      { text: '区县', value: 'DISTRICT' }
    ],
    props: {
      placeholder: '请选择服务范围'
    }
  }
]

// 数据加载函数
const loadAgencyData = async (params) => {
  try {
    console.log('加载代理机构数据:', params)

    // 构建查询参数
    const queryParams = {
      page: params.page,
      pageSize: params.pageSize
    }

    // 添加搜索关键词
    if (params.keyword && params.keyword.trim()) {
      queryParams.keyword = params.keyword.trim()
    }

    // 添加筛选条件
    if (params.enabled !== undefined && params.enabled !== '') {
      queryParams.enabled = params.enabled
    }

    if (params.serviceRange && params.serviceRange !== '') {
      queryParams.serviceRange = params.serviceRange
    }

    const response = await agencyApi.getAgencyList(queryParams)

    // 处理返回数据
    return {
      data: response.data || response.list || [],
      total: response.total || response.count || 0
    }

  } catch (error) {
    console.error('获取代理机构列表失败:', error)
    throw error
  }
}

// 事件处理方法
const handleAgencyClick = (agency, index) => {
  console.log('点击代理机构:', agency, index)
  handleAgencyDetail(agency)
}

const handleAgencyDetail = (agency) => {
  router.push(`/gov/agency/detail/${agency.id}`)
}

const handleAddAgency = () => {
  router.push('/gov/agency/add')
}

const getServiceRangeText = (range) => {
  return serviceRangeMap[range] || range
}
</script>

<style lang="scss" scoped>
.agency-list-page {
  height: 100vh;
  background-color: var(--van-background);
}

.agency-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.agency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.agency-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  flex: 1;
  margin-right: 12px;
}

.agency-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: var(--van-gray-3);
  color: var(--van-text-color-2);

  &.active {
    background-color: var(--van-success-color);
    color: white;
  }
}

.agency-info {
  .info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .label {
    color: var(--van-text-color-2);
    width: 80px;
    flex-shrink: 0;
  }

  .value {
    color: var(--van-text-color);
    flex: 1;
  }
}
</style>

<route lang="json5">
{
  name: 'AgencyList',
  meta: {
    title: '代理机构列表'
  }
}
</route>
