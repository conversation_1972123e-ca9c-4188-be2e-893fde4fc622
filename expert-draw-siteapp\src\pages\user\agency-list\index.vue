<!-- 抽取代理机构列表页面 -->
<template>
  <div class="agency-list-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="模糊检索项目名称"
        @search="onSearch"
        @clear="onClear"
      />
      <div class="filter-icon" @click="showFilterPopup = true">
        <van-icon name="wap-nav" />
        <span>筛选</span>
      </div>
    </div>

    <!-- 状态标签 -->
    <div class="status-tabs">
      <div 
        class="tab-item"
        :class="{ active: currentStatus === 'NORMAL' }"
        @click="switchStatus('NORMAL')"
      >
        <span>正常</span>
        <div class="tab-line"></div>
      </div>
      <div 
        class="tab-item"
        :class="{ active: currentStatus === 'CANCEL' }"
        @click="switchStatus('CANCEL')"
      >
        <span>已作废</span>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="project-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div 
            v-for="item in projectList" 
            :key="item.id"
            class="project-item"
          >
            <!-- 项目标题和状态 -->
            <div class="project-header">
              <div class="project-title">{{ item.name || '这里是项目名称这里是项目' }}</div>
              <div class="project-status" :class="getStatusClass(item)">
                {{ getStatusText(item) }}
              </div>
            </div>

            <!-- 项目信息 -->
            <div class="project-info">
              <div class="info-row">
                <span class="label">项目分类</span>
                <span class="value">{{ getProjectType(item.type) }}</span>
              </div>
              <div class="info-row">
                <span class="label">直属单位</span>
                <span class="value">{{ item.department?.name || '张三' }}</span>
              </div>
              <div class="info-row">
                <span class="label">承办处室</span>
                <span class="value">{{ item.department?.parent?.name || '经办时间' }}</span>
              </div>
              <div class="info-row">
                <span class="label">成都市农业农村局</span>
                <span class="value">2025-12-12 12:22:22</span>
              </div>
              <div class="info-row">
                <span class="label">采购品目</span>
                <span class="value">{{ item.purpose || '采购内容（用途）' }}</span>
              </div>
              <div class="info-row">
                <span class="label">办公用品</span>
                <span class="value">{{ item.items || '设备更新' }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <van-button 
                v-if="!item.drew"
                class="action-btn secondary"
                @click="handleEdit(item)"
              >
                作废
              </van-button>
              <van-button 
                v-if="!item.drew"
                type="primary"
                class="action-btn primary"
                @click="handleApply(item)"
              >
                采购代理申请
              </van-button>
              <van-button 
                v-else
                class="action-btn detail"
                @click="handleViewDetail(item)"
              >
                申请详情
              </van-button>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 底部添加按钮 -->
    <div class="bottom-add-button">
      <van-button 
        type="primary" 
        class="add-btn"
        @click="handleAddProject"
      >
        <van-icon name="plus" />
        无预算项目前期工作抽取代理机构
      </van-button>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup 
      v-model:show="showFilterPopup" 
      position="right" 
      :style="{ width: '80%', height: '100%' }"
    >
      <div class="filter-content">
        <div class="filter-header">
          <span>筛选</span>
          <van-icon name="cross" @click="showFilterPopup = false" />
        </div>
        <!-- 筛选内容可以后续扩展 -->
        <div class="filter-body">
          <p>筛选功能开发中...</p>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { expertApi } from '@/api/expert'

const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const currentStatus = ref('NORMAL')
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const showFilterPopup = ref(false)
const projectList = ref([])

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 20
})

// 搜索
const onSearch = () => {
  resetList()
  loadProjectList()
}

// 清空搜索
const onClear = () => {
  searchKeyword.value = ''
  resetList()
  loadProjectList()
}

// 切换状态
const switchStatus = (status) => {
  currentStatus.value = status
  resetList()
  loadProjectList()
}

// 下拉刷新
const onRefresh = () => {
  resetList()
  loadProjectList().finally(() => {
    refreshing.value = false
  })
}

// 加载更多
const onLoad = () => {
  loadProjectList()
}

// 重置列表
const resetList = () => {
  projectList.value = []
  pagination.page = 1
  finished.value = false
  loading.value = false
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      projectStatus: currentStatus.value,
      timestamp: Date.now()
    }
    
    // 调用真实API
    const response = await expertApi.getProjectList(params)
    
    // 处理响应数据
    const data = Array.isArray(response) ? response : (response.data || [])

    if (pagination.page === 1) {
      projectList.value = data
    } else {
      projectList.value.push(...data)
    }

    pagination.page++

    // 判断是否还有更多数据
    if (data.length < pagination.pageSize) {
      finished.value = true
    }
    
  } catch (error) {
    console.error('加载项目列表失败:', error)
    showToast('加载失败，请重试')
  } finally {
    // loading.value = false
  }
}

// 模拟API调用
const mockApiCall = (params) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = Array.from({ length: params.pageSize }, (_, index) => ({
        id: `${params.page}_${index}`,
        name: '这里是项目名称这里是项目',
        type: 'GOVERNMENT',
        department: {
          name: '张三',
          parent: { name: '经办时间' }
        },
        purpose: '采购内容（用途）',
        items: '设备更新',
        projectStatus: params.projectStatus,
        drew: Math.random() > 0.5
      }))
      
      resolve({
        data: params.page > 3 ? [] : mockData // 模拟3页数据
      })
    }, 1000)
  })
}

// 获取项目类型文本
const getProjectType = (type) => {
  const typeMap = {
    'GOVERNMENT': '经办人',
    'DIRECTLY': '直属单位'
  }
  return typeMap[type] || '经办人'
}

// 获取状态样式类
const getStatusClass = (item) => {
  if (item.drew) return 'applied'
  return item.projectStatus === 'NORMAL' ? 'normal' : 'cancelled'
}

// 获取状态文本
const getStatusText = (item) => {
  if (item.drew) return '申请详情'
  return item.projectStatus === 'NORMAL' ? '未申请' : '已作废'
}

// 处理编辑（作废）
const handleEdit = (item) => {
  showToast('作废功能开发中')
}

// 处理申请
const handleApply = (item) => {
  router.push(`/user/expert-apply?projectId=${item.id}`)
}

// 查看详情
const handleViewDetail = (item) => {
  router.push(`/user/expert-apply/detail/${item.mainApplyId || item.id}`)
}

// 添加项目
const handleAddProject = () => {
  router.push('/user/agency-add')
}

// 页面初始化
onMounted(() => {
  loadProjectList()
})
</script>

<style lang="scss" scoped>
.agency-list-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.search-section {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;

  :deep(.van-search) {
    flex: 1;
    padding: 0;
    background: #f7f8fa;

    .van-search__content {
      background: #f7f8fa;
      border-radius: 20px;
    }

    .van-field__control {
      font-size: 14px;
    }
  }

  .filter-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 12px;
    padding: 8px;
    cursor: pointer;

    .van-icon {
      font-size: 18px;
      color: #646566;
      margin-bottom: 2px;
    }

    span {
      font-size: 12px;
      color: #646566;
    }
  }
}

.status-tabs {
  display: flex;
  background: white;
  border-bottom: 1px solid #f0f0f0;

  .tab-item {
    flex: 1;
    position: relative;
    padding: 16px 0;
    text-align: center;
    cursor: pointer;

    span {
      font-size: 16px;
      color: #646566;
      font-weight: 500;
    }

    .tab-line {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 3px;
      background: transparent;
      border-radius: 2px;
    }

    &.active {
      span {
        color: #07c160;
      }

      .tab-line {
        background: #07c160;
      }
    }
  }
}

.project-list {
  padding: 12px 16px;
}

.project-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;

  .project-title {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    line-height: 1.4;
    margin-right: 12px;
  }

  .project-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;

    &.normal {
      background: #f0f9ff;
      color: #1890ff;
    }

    &.applied {
      background: #f6ffed;
      color: #52c41a;
    }

    &.cancelled {
      background: #fff2f0;
      color: #ff4d4f;
    }
  }
}

.project-info {
  margin-bottom: 16px;

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f7f8fa;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 14px;
      color: #646566;
      min-width: 80px;
    }

    .value {
      font-size: 14px;
      color: #323233;
      text-align: right;
      flex: 1;
      margin-left: 12px;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 12px;

  .action-btn {
    flex: 1;
    height: 36px;
    border-radius: 6px;
    font-size: 14px;

    &.secondary {
      background: #f7f8fa;
      color: #646566;
      border: 1px solid #ebedf0;
    }

    &.primary {
      background: #07c160;
      border: none;
    }

    &.detail {
      background: #ff8c00;
      color: white;
      border: none;
    }
  }
}

.bottom-add-button {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  z-index: 100;

  .add-btn {
    width: 100%;
    height: 44px;
    background: #07c160;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .van-icon {
      font-size: 16px;
    }
  }
}

.filter-content {
  height: 100%;
  display: flex;
  flex-direction: column;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    span {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
    }

    .van-icon {
      font-size: 18px;
      color: #646566;
      cursor: pointer;
    }
  }

  .filter-body {
    flex: 1;
    padding: 16px;
  }
}

// 覆盖vant样式
:deep(.van-pull-refresh) {
  min-height: calc(100vh - 200px);
}

:deep(.van-list) {
  padding: 0;
}
</style>
