<!-- 专家资料库列表页面 -->
<template>
  <div class="expert-list-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索专家姓名"
        @search="handleSearch"
        @clear="handleClear"
        show-action
      >
        <template #action>
          <div @click="handleSearch" class="search-action">筛选</div>
        </template>
      </van-search>
    </div>

    <!-- 专家列表 -->
    <div class="expert-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="expert in expertList"
            :key="expert.id"
            class="expert-card"
            @click="handleExpertDetail(expert)"
          >
            <div class="expert-header">
              <div class="expert-avatar">
                <van-icon name="contact" size="20" />
              </div>
              <div class="expert-basic">
                <div class="expert-name">{{ expert.name }}</div>
                <div class="expert-info">
                  <span class="gender">{{ getGenderText(expert.gender) }}</span>
                  <span class="birthday">{{ formatBirthday(expert.birthday) }}</span>
                  <span class="id-num">{{ maskIdNumber(expert.idNum) }}</span>
                </div>
              </div>
              <div class="expert-status" :class="getStatusClass(expert.enabled)">
                {{ getStatusText(expert.enabled) }}
              </div>
            </div>
            
            <div class="expert-details">
              <div class="detail-row">
                <div class="detail-item">
                  <div class="detail-label">服务范围</div>
                  <div class="detail-value">{{ getServiceRangeText(expert.serviceRange) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">专家类型</div>
                  <div class="detail-value">{{ expert.type?.name || '未分类' }}</div>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <div class="detail-label">单位职务</div>
                  <div class="detail-value">{{ expert.post || '暂无' }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">所在领域</div>
                  <div class="detail-value">{{ expert.field || '暂无' }}</div>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && expertList.length === 0"
      description="暂无专家数据"
      image="search"
    />

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAddExpert"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { expertApi } from '@/api/expert'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const expertList = ref([])
const currentPage = ref(1)
const pageSize = ref(20)

// 方法
const fetchExpertList = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1
      finished.value = false
    }
    
    loading.value = true
    const params = {
      pageSize: pageSize.value.toString()
    }
    
    if (searchKeyword.value.trim()) {
      params.name = searchKeyword.value.trim()
    }
    
    const response = await expertApi.getExpertList(params)
    
    if (isRefresh) {
      expertList.value = response.list || response || []
    } else {
      expertList.value.push(...(response.list || response || []))
    }
    
    // 判断是否还有更多数据
    if (!response.list || response.list.length < pageSize.value) {
      finished.value = true
    }
    
  } catch (error) {
    console.error('获取专家列表失败:', error)
    showToast('获取数据失败')
    
    // 使用模拟数据
    if (isRefresh || expertList.value.length === 0) {
      expertList.value = [
        {
          id: '1',
          type: { id: '1', name: '农业测试类' },
          name: '张三',
          gender: 'MALE',
          birthday: '1978-12-12',
          idNum: '0101**********0029',
          post: '高级工程师',
          field: '农业',
          level: '高级',
          phone: '138****5678',
          enabled: true,
          serviceRange: 'ALL',
          createTime: '2024-01-15T10:30:00'
        },
        {
          id: '2',
          type: { id: '1', name: '农业测试类' },
          name: '李四',
          gender: 'MALE',
          birthday: '1978-12-12',
          idNum: '0101**********0029',
          post: '高级工程师',
          field: '农业',
          level: '高级',
          phone: '139****1234',
          enabled: false,
          serviceRange: 'DIRECTLY',
          createTime: '2024-01-16T14:20:00'
        },
        {
          id: '3',
          type: { id: '1', name: '农业测试类' },
          name: '王五',
          gender: 'MALE',
          birthday: '1978-12-12',
          idNum: '0101**********0029',
          post: '高级工程师',
          field: '农业',
          level: '高级',
          phone: '137****9876',
          enabled: true,
          serviceRange: 'DIRECTLY',
          createTime: '2024-01-17T09:15:00'
        }
      ]
      finished.value = true
    }
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onLoad = () => {
  if (!finished.value) {
    currentPage.value++
    fetchExpertList()
  }
}

const onRefresh = () => {
  fetchExpertList(true)
}

const handleSearch = () => {
  fetchExpertList(true)
}

const handleClear = () => {
  searchKeyword.value = ''
  fetchExpertList(true)
}

const handleExpertDetail = (expert) => {
  router.push(`/gov/expert/detail/${expert.id}`)
}

const handleAddExpert = () => {
  router.push('/gov/expert/add')
}

const getGenderText = (gender) => {
  return gender === 'MALE' ? '男' : gender === 'FEMALE' ? '女' : '未知'
}

const formatBirthday = (birthday) => {
  if (!birthday) return ''
  return birthday.replace(/\//g, '-')
}

const maskIdNumber = (idNum) => {
  if (!idNum) return ''
  return idNum
}

const getStatusText = (enabled) => {
  return enabled ? '已启用' : '已禁用'
}

const getStatusClass = (enabled) => {
  return enabled ? 'enabled' : 'disabled'
}

const getServiceRangeText = (range) => {
  const rangeMap = {
    'ALL': '全部',
    'DIRECTLY': '局机关和直属单位',
    'DISTRICT': '区县'
  }
  return rangeMap[range] || range
}

// 生命周期
onMounted(() => {
  fetchExpertList(true)
})

onActivated(() => {
  fetchExpertList(true)
})
</script>

<style lang="scss" scoped>
.expert-list-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.search-section {
  background: white;
  padding: 8px 0;
  border-bottom: 1px solid var(--van-border-color);
  
  .search-action {
    color: var(--van-text-color);
    font-size: 14px;
  }
}

.expert-list {
  padding: 8px 16px;
}

.expert-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.expert-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.expert-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--van-gray-4);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--van-text-color-2);
  margin-right: 12px;
  flex-shrink: 0;
}

.expert-basic {
  flex: 1;
}

.expert-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  margin-bottom: 4px;
}

.expert-info {
  font-size: 12px;
  color: var(--van-text-color-2);
  
  span {
    margin-right: 8px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

.expert-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  flex-shrink: 0;
  
  &.enabled {
    background-color: #E8F5E8;
    color: #4CAF50;
  }
  
  &.disabled {
    background-color: #FFF3E0;
    color: #FF9800;
  }
}

.expert-details {
  .detail-row {
    display: flex;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-item {
    flex: 1;
    
    &:first-child {
      margin-right: 16px;
    }
  }

  .detail-label {
    font-size: 12px;
    color: var(--van-text-color-2);
    margin-bottom: 2px;
  }

  .detail-value {
    font-size: 14px;
    color: var(--van-text-color);
  }
}
</style>

<route lang="json5">
{
  name: 'ExpertDatabase',
  meta: {
    title: '专家资料库'
  }
}
</route>
