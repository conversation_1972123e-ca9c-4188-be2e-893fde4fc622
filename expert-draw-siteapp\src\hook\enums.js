/**
 * 枚举管理器
 * 用于管理系统中的所有枚举值
 */

import { enumsApi } from "@/api/enums";

export default function useEnums() {
  let enums = new Map();
  /**
   * 初始化枚举数据
   */
  async function init() {
    const res = await enumsApi.getAllEnums();
    // 将枚举数据存储到Map中，以enumType为key
    res.data.forEach((enumGroup) => {
      enums.set(enumGroup.enumType, enumGroup.enums);
    });
    return true;
  }
  /**
   * 获取指定类型的枚举值
   * @param {string} enumType 枚举类型
   * @returns {Array} 枚举值数组
   */
  function getEnums(enumType) {
    return (enums.get(enumType) || []).map((x) => {
      return {
        ...x,
        text: x.enumDesc,
        value: x.enumName,
      };
    });
  }
  /**
   * 获取枚举值的文本
   * @param {string} enumType 枚举类型
   * @param {string} enumName 枚举值
   * @returns {string} 枚举值的文本
   */
  function getEnumText(enumType, enumName) {
    const enums = getEnums(enumType);
    const item = enums.find((x) => x.enumName === enumName);
    return item ? item.enumDesc : "";
  }
  /**
   * 获取枚举值的颜色
   * @param {string} enumType 枚举类型
   * @param {string} enumName 枚举值
   * @returns {string} 枚举值的文本
   */
  function getEnumColor(enumType, enumName) {
    const enums = getEnums(enumType);
    const item = enums.find((x) => x.enumName === enumName);
    return item ? item.enumColor : "";
  }
  return {
    init,
    getEnums,
    getEnumText,
    getEnumColor,
  };
}
