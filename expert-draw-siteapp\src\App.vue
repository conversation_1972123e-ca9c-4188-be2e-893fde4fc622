<script setup>
import { onMounted } from 'vue'
import { useUserStore } from '@/stores'

const userStore = useUserStore()

// 应用启动时获取用户信息
onMounted(async () => {
  try {
    await userStore.fetchUserInfo()
  } catch (error) {
    console.error('初始化用户信息失败:', error)
  }
})
</script>

<template>
 <van-config-provider>
    <nav-bar />
    <router-view v-slot="{ Component }">
      <section class="app-wrapper">
        <!-- <keep-alive> -->
          <component :is="Component" />
        <!-- </keep-alive> -->
      </section>
    </router-view>
  </van-config-provider>
</template>
<style scoped>
.app-wrapper {
  width: 100%;
  position: relative;
  background-color: var(--van-background);
}
</style>

