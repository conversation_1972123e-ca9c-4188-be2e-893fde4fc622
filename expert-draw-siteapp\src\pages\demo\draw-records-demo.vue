<!-- 名单管理列表示例 -->
<template>
  <div class="draw-records-demo">
    <van-nav-bar title="名单管理示例" left-arrow @click-left="$router.back()" />
    
    <FuniList
      ref="recordsListRef"
      :tabs="tabsConfig"
      item-key="id"
      empty-text="暂无抽取记录"
      @item-click="handleRecordClick"
    >
      <template #item="{ item: record }">
        <div class="record-card">
          <!-- 状态标签 -->
          <div class="status-tags">
            <van-tag 
              v-for="tag in getStatusTags(record)" 
              :key="tag.text"
              :type="tag.type" 
              size="small"
              class="status-tag"
            >
              {{ tag.text }}
            </van-tag>
          </div>

          <!-- 项目信息 -->
          <div class="project-info">
            <div class="project-header">
              <span class="project-code">项目编号：{{ record.code }}</span>
              <span class="project-status" :class="getFlowStatusClass(record.flowStatus)">
                {{ getFlowStatusText(record.flowStatus) }}
              </span>
            </div>
            <div class="project-name">{{ record.name }}</div>
          </div>

          <!-- 详细信息 -->
          <div class="detail-info">
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">项目类型</span>
                <span class="info-value">{{ getProjectTypeText(record.type) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">经办人</span>
                <span class="info-value">{{ record.createUser?.nickname || '未知' }}</span>
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">抽取专家</span>
                <span class="info-value">{{ record.expertName || '未抽取' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">经办处室</span>
                <span class="info-value">{{ getDepartmentName(record.createUser) }}</span>
              </div>
            </div>
            
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">经办时间</span>
                <span class="info-value">{{ formatDateTime(record.createTime) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">流程状态</span>
                <span class="info-value">{{ getFlowStatusText(record.flowStatus) }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-section">
            <van-button 
              type="default" 
              size="small" 
              @click.stop="handleDetail(record)"
            >
              详情
            </van-button>
          </div>
        </div>
      </template>

      <template #footer>
        <!-- 浮动添加按钮 -->
        <van-floating-bubble
          axis="xy"
          icon="plus"
          @click="handleAddRecord"
        />
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import FuniList from '@/components/FuniList.vue'

const router = useRouter()
const recordsListRef = ref()

// 模拟数据生成
const generateMockRecords = (page = 1, pageSize = 20) => {
  const records = []
  const projectNames = [
    '2024年市级供销合作社培育壮大工程专项资金',
    '农业产业化发展专项资金项目',
    '乡村振兴示范区建设项目',
    '现代农业园区建设专项资金',
    '农村人居环境整治提升项目'
  ]
  
  const flowStatuses = ['UN_SUBMIT', 'APPROVAL', 'FINISH', 'REJECT', 'REVOKE']
  const types = ['REVIEW', 'PURCHASE', 'EVADE', 'REVOKE']
  
  for (let i = 0; i < pageSize; i++) {
    const index = (page - 1) * pageSize + i
    const flowStatus = flowStatuses[index % flowStatuses.length]
    const type = types[index % types.length]
    
    records.push({
      id: `record_${index + 1}`,
      entityId: `entity_${index + 1}`,
      name: `${projectNames[index % projectNames.length]} ${Math.floor(index / projectNames.length) + 1}`,
      code: `${88839003000 + index}`,
      type: type,
      initFlag: index % 3 === 0,
      flowStatus: flowStatus,
      flowUser: {
        id: `user_${index + 1}`,
        nickname: `经办人${index + 1}`,
        post: ['科员', '主任科员', '副主任', '处长'][index % 4]
      },
      flowTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      expertName: index % 4 === 0 ? null : `专家${index + 1}`,
      createUser: {
        id: `creator_${index + 1}`,
        nickname: `创建人${index + 1}`,
        post: ['科员', '主任科员', '副主任', '处长'][index % 4],
        groups: [{
          name: ['成都市农业农村局', '成都市财政局', '成都市发改委'][index % 3]
        }]
      },
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    })
  }
  
  return records
}

// 数据加载函数
const loadDrawRecordsData = async (params) => {
  try {
    console.log('加载抽取记录数据:', params)
    
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    let mockData = generateMockRecords(params.page, params.pageSize)
    
    // 模拟搜索过滤
    if (params.keyword && params.keyword.trim()) {
      const keyword = params.keyword.trim().toLowerCase()
      mockData = mockData.filter(record => 
        record.name.toLowerCase().includes(keyword) ||
        record.code.includes(keyword)
      )
    }
    
    // 模拟流程状态过滤
    if (params.flowStatus && params.flowStatus !== '') {
      mockData = mockData.filter(record => record.flowStatus === params.flowStatus)
    }
    
    // 模拟项目类型过滤
    if (params.type && params.type !== '') {
      mockData = mockData.filter(record => record.type === params.type)
    }
    
    return {
      data: mockData,
      total: 200 // 模拟总数
    }
    
  } catch (error) {
    console.error('获取抽取记录失败:', error)
    throw error
  }
}

// Tabs 配置
const tabsConfig = [
  {
    key: 'records',
    title: '抽取记录',
    loadFunction: loadDrawRecordsData,
    searchPlaceholder: '搜索项目名称/编号',
    keyword: 'keyword',
    filterConfig: [
      {
        type: 'select',
        label: '流程状态',
        prop: 'flowStatus',
        options: [
          { text: '全部', value: '' },
          { text: '未提交', value: 'UN_SUBMIT' },
          { text: '审批中', value: 'APPROVAL' },
          { text: '已通过', value: 'FINISH' },
          { text: '已驳回', value: 'REJECT' },
          { text: '已撤回', value: 'REVOKE' }
        ],
        props: {
          placeholder: '请选择流程状态'
        }
      },
      {
        type: 'select',
        label: '项目类型',
        prop: 'type',
        options: [
          { text: '全部', value: '' },
          { text: '抽取专家', value: 'REVIEW' },
          { text: '抽取代理机构', value: 'PURCHASE' },
          { text: '专家规避', value: 'EVADE' },
          { text: '代理机构撤回', value: 'REVOKE' }
        ],
        props: {
          placeholder: '请选择项目类型'
        }
      }
    ]
  }
]

// 事件处理方法
const handleRecordClick = (record, index) => {
  console.log('点击抽取记录:', record, index)
  showToast(`点击了: ${record.name}`)
}

const handleDetail = (record) => {
  showToast(`查看详情: ${record.name}`)
}

const handleAddRecord = () => {
  showToast('添加抽取记录功能')
}

// 工具方法
const getStatusTags = (record) => {
  const tags = []
  
  // 根据不同状态添加标签
  if (record.initFlag) {
    tags.push({ text: '本级', type: 'primary' })
  }
  
  if (record.flowStatus === 'APPROVAL') {
    tags.push({ text: '审批中', type: 'warning' })
  } else if (record.flowStatus === 'FINISH') {
    tags.push({ text: '审批通过', type: 'success' })
  } else if (record.flowStatus === 'REJECT') {
    tags.push({ text: '已驳回', type: 'danger' })
  }
  
  return tags
}

const getFlowStatusText = (status) => {
  const statusMap = {
    'UN_SUBMIT': '未提交',
    'APPROVAL': '审批中',
    'FINISH': '已通过',
    'REJECT': '已驳回',
    'REVOKE': '已撤回',
    'REJECT_FINISH': '驳回完成'
  }
  return statusMap[status] || status
}

const getFlowStatusClass = (status) => {
  const classMap = {
    'UN_SUBMIT': 'status-draft',
    'APPROVAL': 'status-pending',
    'REJECT': 'status-rejected',
    'REVOKE': 'status-revoked',
    'FINISH': 'status-approved',
    'REJECT_FINISH': 'status-rejected'
  }
  return classMap[status] || ''
}

const getProjectTypeText = (type) => {
  const typeMap = {
    'REVIEW': '抽取专家',
    'PURCHASE': '抽取代理机构',
    'EVADE': '专家规避',
    'REVOKE': '代理机构撤回'
  }
  return typeMap[type] || type
}

const getDepartmentName = (user) => {
  return user?.groups?.[0]?.name || '未知部门'
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-')
}
</script>

<style lang="scss" scoped>
.draw-records-demo {
  height: 100vh;
  background-color: var(--van-background);
}

.record-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.status-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  
  .status-tag {
    font-size: 10px;
  }
}

.project-info {
  margin-bottom: 12px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.project-code {
  font-size: 12px;
  color: var(--van-text-color-2);
}

.project-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
  &.status-pending {
    background-color: #FFF3E0;
    color: #FF9800;
  }
  
  &.status-approved {
    background-color: #E8F5E8;
    color: #4CAF50;
  }
  
  &.status-rejected {
    background-color: #FFEBEE;
    color: #F44336;
  }
  
  &.status-draft {
    background-color: var(--van-gray-1);
    color: var(--van-text-color-2);
  }
  
  &.status-revoked {
    background-color: #F3E5F5;
    color: #9C27B0;
  }
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  line-height: 1.4;
}

.detail-info {
  margin-bottom: 12px;
  
  .info-row {
    display: flex;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    
    &:first-child {
      margin-right: 16px;
    }
  }

  .info-label {
    font-size: 12px;
    color: var(--van-text-color-2);
    margin-bottom: 2px;
    display: block;
  }

  .info-value {
    font-size: 14px;
    color: var(--van-text-color);
  }
}

.action-section {
  display: flex;
  justify-content: flex-end;
  padding-top: 8px;
  border-top: 1px solid var(--van-border-color);
}
</style>

<route lang="json5">
{
  name: 'DrawRecordsDemo',
  meta: {
    title: '名单管理示例'
  }
}
</route>
