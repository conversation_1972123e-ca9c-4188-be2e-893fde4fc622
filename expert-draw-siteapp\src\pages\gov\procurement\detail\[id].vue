<!-- 采购项目详情页面 -->
<template>
  <div class="procurement-detail-page">
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>
    
    <div v-else-if="projectDetail" class="detail-content">
      <!-- 基本信息 -->
      <van-cell-group inset title="基本信息">
        <van-cell title="项目名称" :value="projectDetail.name" />
        <van-cell title="项目分类" :value="projectDetail.category" />
        <van-cell title="项目状态">
          <template #value>
            <van-tag :type="getStatusType(projectDetail.status)">
              {{ getStatusText(projectDetail.status) }}
            </van-tag>
          </template>
        </van-cell>
        <van-cell title="采购日期" :value="formatDate(projectDetail.procurementDate)" />
        <van-cell title="项目预算" :value="projectDetail.budget ? `${projectDetail.budget}万元` : '未填写'" />
      </van-cell-group>

      <!-- 采购单位信息 -->
      <van-cell-group inset title="采购单位信息">
        <van-cell title="采购单位" :value="projectDetail.procuringEntity || '未填写'" />
        <van-cell title="联系人" :value="projectDetail.contactPerson || '未填写'" />
        <van-cell title="联系电话" :value="projectDetail.contactPhone || '未填写'" />
        <van-cell title="单位地址" :value="projectDetail.entityAddress || '未填写'" />
      </van-cell-group>

      <!-- 经办信息 -->
      <van-cell-group inset title="经办信息">
        <van-cell title="经办人" :value="projectDetail.handler || '未知'" />
        <van-cell title="经办部门" :value="projectDetail.handlerDepartment || '未知'" />
        <van-cell title="经办时间" :value="formatDateTime(projectDetail.handleTime)" />
        <van-cell title="创建时间" :value="formatDateTime(projectDetail.createTime)" />
      </van-cell-group>

      <!-- 项目描述 -->
      <van-cell-group inset title="项目描述" v-if="projectDetail.description">
        <van-cell title="项目描述" :value="projectDetail.description" />
      </van-cell-group>

      <!-- 审批信息 -->
      <van-cell-group inset title="审批信息" v-if="projectDetail.approver">
        <van-cell title="审批人" :value="projectDetail.approver || '未知'" />
        <van-cell title="审批时间" :value="formatDateTime(projectDetail.approveTime)" />
        <van-cell title="审批意见" :value="projectDetail.approveReason || '无'" />
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-section">
        <van-button
          v-if="canEdit(projectDetail)"
          type="primary"
          size="large"
          block
          @click="handleEdit"
        >
          编辑
        </van-button>
        
        <van-button
          v-if="canSubmit(projectDetail)"
          type="success"
          size="large"
          block
          @click="handleSubmit"
          :loading="submitLoading"
        >
          提交审批
        </van-button>
        
        <van-button
          v-if="canRevoke(projectDetail)"
          type="warning"
          size="large"
          block
          @click="handleRevoke"
          :loading="revokeLoading"
        >
          撤回申请
        </van-button>
        
        <van-button
          v-if="canDelete(projectDetail)"
          type="danger"
          size="large"
          block
          @click="handleDelete"
          :loading="deleteLoading"
        >
          删除
        </van-button>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-else
      description="采购项目不存在"
      image="error"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { procurementApi } from '@/api/procurement'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const revokeLoading = ref(false)
const deleteLoading = ref(false)
const projectDetail = ref(null)

// 方法
const fetchProjectDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    const response = await procurementApi.getProjectDetail(id)
    projectDetail.value = response
  } catch (error) {
    console.error('获取采购项目详情失败:', error)
    showToast('获取详情失败')
    
    // 使用模拟数据
    projectDetail.value = {
      id: route.params.id,
      name: '抽取招标代理机构和评审专家',
      category: '测试无审请项目',
      status: 'DRAFT',
      procurementDate: '2025-12-12',
      budget: '100',
      procuringEntity: '成都市农业农村局',
      contactPerson: '张三',
      contactPhone: '138****5678',
      entityAddress: '成都市青羊区',
      handler: '张三',
      handlerDepartment: '成都市农业农村局',
      handleTime: '2025-12-12 12:22:22',
      createTime: '2025-12-12 12:22:22',
      description: '这是一个测试采购项目的详细描述信息。',
      approver: null,
      approveTime: null,
      approveReason: null
    }
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push(`/gov/procurement/edit/${route.params.id}`)
}

const handleSubmit = async () => {
  try {
    await showConfirmDialog({
      title: '确认提交',
      message: '确定要提交该采购项目进行审批吗？'
    })
    
    submitLoading.value = true
    await procurementApi.submitProject(route.params.id)
    
    showSuccessToast('提交成功')
    fetchProjectDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      showToast('提交失败，请重试')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleRevoke = async () => {
  try {
    await showConfirmDialog({
      title: '确认撤回',
      message: '确定要撤回该采购项目申请吗？'
    })
    
    revokeLoading.value = true
    await procurementApi.revokeProject(route.params.id)
    
    showSuccessToast('撤回成功')
    fetchProjectDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤回失败:', error)
      showToast('撤回失败，请重试')
    }
  } finally {
    revokeLoading.value = false
  }
}

const handleDelete = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '删除后无法恢复，确定要删除该采购项目吗？'
    })
    
    deleteLoading.value = true
    await procurementApi.deleteProject(route.params.id)
    
    showSuccessToast('删除成功')
    router.back()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      showToast('删除失败，请重试')
    }
  } finally {
    deleteLoading.value = false
  }
}

// 权限判断方法
const canEdit = (project) => {
  return project.status === 'DRAFT' || project.status === 'REJECTED'
}

const canSubmit = (project) => {
  return project.status === 'DRAFT'
}

const canRevoke = (project) => {
  return project.status === 'PENDING'
}

const canDelete = (project) => {
  return project.status === 'DRAFT' || project.status === 'REJECTED'
}

const getStatusText = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'PENDING': '待审批',
    'APPROVED': '已通过',
    'REJECTED': '已驳回',
    'COMPLETED': '已完成'
  }
  return statusMap[status] || status
}

const getStatusType = (status) => {
  const typeMap = {
    'DRAFT': 'default',
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'COMPLETED': 'primary'
  }
  return typeMap[status] || 'default'
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  // 如果已经是格式化的字符串，直接返回
  if (dateString.includes('-') && dateString.includes(':')) {
    return dateString
  }
  // 否则格式化日期
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  // 如果已经是格式化的字符串，直接返回
  if (dateString.includes('-')) {
    return dateString
  }
  // 否则格式化日期
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN').replace(/\//g, '-')
}

// 生命周期
onMounted(() => {
  fetchProjectDetail()
})

onActivated(() => {
  fetchProjectDetail()
})
</script>

<style lang="scss" scoped>
.procurement-detail-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  .van-cell-group {
    margin-bottom: 16px;
  }
}

.action-section {
  margin-top: 32px;
  
  .van-button {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

<route lang="json5">
{
  name: 'ProcurementDetail',
  meta: {
    title: '采购项目详情'
  }
}
</route>
