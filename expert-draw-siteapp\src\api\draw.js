/**
 * 抽取记录相关API
 */

export const drawApi = {
  /**
   * 获取抽取记录列表
   * @param {Object} params - 查询参数
   * @param {string} [params.page] - 页码
   * @param {string} [params.pageSize] - 每页条数
   * @param {string} [params.timestamp] - 时间戳
   * @returns {Promise} 返回抽取记录列表数据
   */
  getDrawRecords: (params = {}) => {
    return window.$http.fetch('/api/v1/apply/draw', params)
  },

  /**
   * 获取抽取记录详情
   * @param {string} id - 记录ID
   * @returns {Promise} 返回抽取记录详情
   */
  getDrawRecordDetail: (id) => {
    return window.$http.fetch(`/api/v1/apply/draw/${id}`)
  },

  /**
   * 创建抽取记录
   * @param {Object} data - 抽取记录数据
   * @returns {Promise} 返回创建结果
   */
  createDrawRecord: (data) => {
    return window.$http.post('/api/v1/apply/draw', data)
  },

  /**
   * 更新抽取记录
   * @param {string} id - 记录ID
   * @param {Object} data - 抽取记录数据
   * @returns {Promise} 返回更新结果
   */
  updateDrawRecord: (id, data) => {
    return window.$http.put(`/api/v1/apply/draw/${id}`, data)
  },

  /**
   * 删除抽取记录
   * @param {string} id - 记录ID
   * @returns {Promise} 返回删除结果
   */
  deleteDrawRecord: (id) => {
    return window.$http.delete(`/api/v1/apply/draw/${id}`)
  },

  /**
   * 提交抽取申请
   * @param {string} id - 记录ID
   * @returns {Promise} 返回提交结果
   */
  submitDrawRecord: (id) => {
    return window.$http.post(`/api/v1/apply/draw/${id}/submit`)
  },

  /**
   * 撤回抽取申请
   * @param {string} id - 记录ID
   * @returns {Promise} 返回撤回结果
   */
  revokeDrawRecord: (id) => {
    return window.$http.post(`/api/v1/apply/draw/${id}/revoke`)
  },

  /**
   * 审批抽取申请
   * @param {string} id - 记录ID
   * @param {Object} data - 审批数据
   * @param {boolean} data.approved - 是否通过
   * @param {string} [data.reason] - 审批意见
   * @returns {Promise} 返回审批结果
   */
  approveDrawRecord: (id, data) => {
    return window.$http.post(`/api/v1/apply/draw/${id}/approve`, data)
  },

  /**
   * 执行抽取
   * @param {string} id - 记录ID
   * @returns {Promise} 返回抽取结果
   */
  executeDrawRecord: (id) => {
    return window.$http.post(`/api/v1/apply/draw/${id}/execute`)
  }
}
