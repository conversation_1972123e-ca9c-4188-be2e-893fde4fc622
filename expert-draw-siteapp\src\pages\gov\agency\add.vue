<!-- 新增代理机构页面 -->
<template>
  <div class="agency-add-page">
    <van-form @submit="handleSubmit" ref="formRef">
      <van-cell-group inset>
        <van-field
          v-model="formData.name"
          name="name"
          label="机构名称"
          placeholder="请输入机构名称"
          :rules="[{ required: true, message: '请输入机构名称' }]"
        />
        
        <van-field
          v-model="formData.leader"
          name="leader"
          label="负责人"
          placeholder="请输入负责人姓名"
          :rules="[{ required: true, message: '请输入负责人姓名' }]"
        />
        
        <van-field
          v-model="formData.leaderPhone"
          name="leaderPhone"
          label="负责人电话"
          placeholder="请输入负责人电话"
          :rules="[
            { required: true, message: '请输入负责人电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]"
        />
        
        <van-field
          v-model="formData.contact"
          name="contact"
          label="经办人1"
          placeholder="请输入经办人1姓名"
          :rules="[{ required: true, message: '请输入经办人1姓名' }]"
        />
        
        <van-field
          v-model="formData.phone"
          name="phone"
          label="经办人1电话"
          placeholder="请输入经办人1电话"
          :rules="[
            { required: true, message: '请输入经办人1电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]"
        />
        
        <van-field
          v-model="formData.contactb"
          name="contactb"
          label="经办人2"
          placeholder="请输入经办人2姓名（可选）"
        />
        
        <van-field
          v-model="formData.phoneb"
          name="phoneb"
          label="经办人2电话"
          placeholder="请输入经办人2电话（可选）"
          :rules="[
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'onBlur' }
          ]"
        />
        
        <van-field
          v-model="formData.serviceRange"
          is-link
          readonly
          name="serviceRange"
          label="服务范围"
          placeholder="请选择服务范围"
          @click="showServiceRangePicker = true"
          :rules="[{ required: true, message: '请选择服务范围' }]"
        />
      </van-cell-group>
      
      <div class="submit-section">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="submitting"
        >
          {{ submitting ? '提交中...' : '提交' }}
        </van-button>
      </div>
    </van-form>

    <!-- 服务范围选择器 -->
    <van-popup v-model:show="showServiceRangePicker" position="bottom">
      <van-picker
        :columns="serviceRangeOptions"
        @confirm="onServiceRangeConfirm"
        @cancel="showServiceRangePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import { agencyApi } from '@/api/agency'

const router = useRouter()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const showServiceRangePicker = ref(false)

const formData = reactive({
  name: '',
  leader: '',
  leaderPhone: '',
  contact: '',
  phone: '',
  contactb: '',
  phoneb: '',
  serviceRange: ''
})

// 服务范围选项
const serviceRangeOptions = [
  { text: '全部', value: 'ALL' },
  { text: '局机关和直属单位', value: 'DIRECTLY' },
  { text: '区县', value: 'DISTRICT' }
]

// 方法
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      name: formData.name.trim(),
      leader: formData.leader.trim(),
      leaderPhone: formData.leaderPhone.trim(),
      contact: formData.contact.trim(),
      phone: formData.phone.trim(),
      contactb: formData.contactb.trim() || '',
      phoneb: formData.phoneb.trim() || '',
      serviceRange: formData.serviceRange,
      enabled: true // 默认启用
    }
    
    // 验证经办人2电话（如果填写了经办人2）
    if (submitData.contactb && !submitData.phoneb) {
      showToast('请输入经办人2的电话')
      return
    }
    
    if (submitData.phoneb && !submitData.contactb) {
      showToast('请输入经办人2的姓名')
      return
    }
    
    await agencyApi.createAgency(submitData)
    
    showSuccessToast('代理机构添加成功')
    
    // 返回列表页面
    router.back()
    
  } catch (error) {
    console.error('添加代理机构失败:', error)
    showToast('添加失败，请重试')
  } finally {
    submitting.value = false
  }
}

const onServiceRangeConfirm = ({ selectedOptions }) => {
  formData.serviceRange = selectedOptions[0]?.value || ''
  showServiceRangePicker.value = false
}

// 获取服务范围显示文本
const getServiceRangeText = (value) => {
  const option = serviceRangeOptions.find(item => item.value === value)
  return option ? option.text : ''
}
</script>

<style lang="scss" scoped>
.agency-add-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 100px;
  flex-shrink: 0;
}

:deep(.van-field__control) {
  text-align: right;
}
</style>

<route lang="json5">
{
  name: 'AgencyAdd',
  meta: {
    title: '新增代理机构'
  }
}
</route>
