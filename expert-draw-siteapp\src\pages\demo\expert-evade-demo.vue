<!-- 专家规避功能演示 -->
<template>
  <div class="expert-evade-demo">
    <van-nav-bar title="专家规避演示" left-arrow @click-left="$router.back()" />
    
    <div class="demo-content">
      <div class="expert-card" @click="handleExpertEvade(mockExpert)">
        <!-- 专家头像和基本信息 -->
        <div class="expert-header">
          <div class="expert-avatar">
            <van-icon name="contact" />
          </div>
          <div class="expert-basic">
            <div class="expert-name">{{ mockExpert.expert.name }}</div>
            <div class="expert-info">
              <span class="gender">{{ mockExpert.expert.gender }}</span>
              <span class="birthday">{{ mockExpert.expert.birthday }}</span>
              <span class="id-num">{{ mockExpert.expert.idNum }}</span>
            </div>
          </div>
        </div>
        
        <!-- 专家详细信息 -->
        <div class="expert-details">
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">专家类型</span>
              <span class="value">{{ mockExpert.expert.type?.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">单位职务</span>
              <span class="value">{{ mockExpert.expert.post }}</span>
            </div>
          </div>
          
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">所在领域</span>
              <span class="value">{{ mockExpert.expert.field }}</span>
            </div>
            <div class="detail-item">
              <span class="label">专业级别</span>
              <span class="value">{{ mockExpert.expert.level }}</span>
            </div>
          </div>
          
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">手机号</span>
              <span class="value">{{ mockExpert.expert.phone }}</span>
            </div>
            <div class="detail-item">
              <span class="label">座机号</span>
              <span class="value">{{ mockExpert.expert.landline }}</span>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="record-actions">
          <van-button size="small" type="warning" plain block @click.stop="handleExpertEvade(mockExpert)">
            专家规避
          </van-button>
        </div>
      </div>
    </div>

    <!-- 专家规避弹框 -->
    <van-popup
      v-model:show="showEvadeDialog"
      position="center"
      round
      :style="{ width: '90%', maxWidth: '400px' }"
    >
      <div class="evade-dialog">
        <div class="dialog-header">
          <h3>对 {{ currentExpert?.expert?.name }} 申请规避</h3>
          <p class="dialog-subtitle">与该评审项目当位在冲突回避</p>
        </div>

        <div class="dialog-content">
          <div class="success-tip">
            <van-icon name="success" color="#07c160" />
            <span>相同专业且具备专业技术资格回避</span>
          </div>

          <div class="reason-input">
            <van-field
              v-model="evadeReason"
              type="textarea"
              placeholder="请输入规避理由"
              rows="3"
              maxlength="200"
              show-word-limit
              autosize
            />
          </div>
        </div>

        <div class="dialog-actions">
          <van-button 
            size="large" 
            @click="showEvadeDialog = false"
            style="margin-right: 12px; flex: 1;"
          >
            返回
          </van-button>
          <van-button 
            type="success" 
            size="large" 
            :loading="evadeLoading"
            @click="confirmEvade"
            style="flex: 2;"
          >
            确定
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { showToast, showSuccessToast } from 'vant'

// 模拟专家数据
const mockExpert = {
  id: 'expert_001',
  expert: {
    name: '张三',
    gender: '男',
    birthday: '1975-03-15',
    idNum: '510***********1234',
    type: {
      name: '技术专家'
    },
    post: '高级工程师',
    field: '软件开发',
    level: '高级职称',
    phone: '138****5678',
    landline: '028-12345678'
  }
}

// 专家规避相关状态
const showEvadeDialog = ref(false)
const currentExpert = ref(null)
const evadeReason = ref('')
const evadeLoading = ref(false)

// 处理专家规避
const handleExpertEvade = (expert) => {
  currentExpert.value = expert
  evadeReason.value = ''
  showEvadeDialog.value = true
}

// 确认专家规避
const confirmEvade = async () => {
  if (!evadeReason.value.trim()) {
    showToast('请输入规避理由')
    return
  }

  try {
    evadeLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    console.log('专家规避数据:', {
      expertId: currentExpert.value.id,
      expertName: currentExpert.value.expert.name,
      reason: evadeReason.value.trim()
    })

    showSuccessToast('专家规避申请成功')
    showEvadeDialog.value = false
    
  } catch (error) {
    console.error('专家规避失败:', error)
    showToast('专家规避失败，请重试')
  } finally {
    evadeLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.expert-evade-demo {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.demo-content {
  padding: 16px;
}

.expert-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.expert-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .expert-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    .van-icon {
      font-size: 24px;
      color: #969799;
    }
  }

  .expert-basic {
    flex: 1;

    .expert-name {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 4px;
    }

    .expert-info {
      font-size: 12px;
      color: #646566;

      span {
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.expert-details {
  .detail-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .detail-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .value {
        font-size: 14px;
        color: #000;
        font-weight: 500;
        
        &:empty::after {
          content: "--";
        }
      }
    }
  }
}

.record-actions {
  padding-top: 12px;
}

// 专家规避弹框样式
.evade-dialog {
  padding: 24px;
  
  .dialog-header {
    text-align: center;
    margin-bottom: 24px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #323233;
      margin: 0 0 8px 0;
    }
    
    .dialog-subtitle {
      font-size: 14px;
      color: #646566;
      margin: 0;
    }
  }
  
  .dialog-content {
    margin-bottom: 24px;
    
    .success-tip {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background-color: #f0f9ff;
      border-radius: 8px;
      margin-bottom: 16px;
      
      .van-icon {
        margin-right: 8px;
        font-size: 16px;
      }
      
      span {
        font-size: 14px;
        color: #07c160;
        font-weight: 500;
      }
    }
    
    .reason-input {
      :deep(.van-field) {
        padding: 12px 16px;
        background-color: #f7f8fa;
        border-radius: 8px;
        border: 1px solid #ebedf0;
        
        .van-field__control {
          font-size: 14px;
          line-height: 1.5;
        }
        
        .van-field__word-limit {
          font-size: 12px;
          color: #969799;
        }
      }
    }
  }
  
  .dialog-actions {
    display: flex;
    gap: 12px;
    
    .van-button {
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>

<route lang="json5">
{
  name: 'ExpertEvadeDemo',
  meta: {
    title: '专家规避演示'
  }
}
</route>
