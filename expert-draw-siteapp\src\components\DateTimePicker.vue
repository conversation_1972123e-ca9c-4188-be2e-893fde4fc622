<!-- 日期时间选择器组件 -->
<template>
  <div class="datetime-picker">
    <!-- 表单字段 -->
    <van-field
      v-model="displayValue"
      placeholder="请选择"
      v-bind="fieldProps"
      readonly
      is-link
      @click="showPicker"
    />
    <van-popup
      v-model:show="data.isPicker"
      position="bottom"
      round
      @close="confirmOn"
      :close-on-click-overlay="false"
    >
      <van-picker
        ref="picker"
        title="请选择时间"
        :columns="data.columns"
        @change="onChange"
        @cancel="cancelOn"
        @confirm="onConfirm"
        v-model="data.selectedValues"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, watch, ref } from "vue";
import moment from 'moment'

const data = reactive({
  isPicker: false, //是否显示弹出层
  columns: [], //所有时间列
  selectedValues: [], //控件选择的时间值
});
const props = defineProps({
  // 绑定值
  modelValue: {
    type: Array,
    default: () => []
  },
  // 传入的显影状态
  showPicker: {
    type: Boolean,
    default: false,
  },
  // 传入的值
  values: {
    type: String,
    default: "",
  },
  fieldProps: {
    type: Object,
    default: () => ({}),
  },
});

const displayValue = ref("");
//定义要向父组件传递的事件
const emit = defineEmits(["update:modelValue", "confirm"]);
watch(
  () => props.showPicker,
  (val) => {
    data.isPicker = val;
    data.columns = [];
    getcolumns();
  },
  {
    immediate: true, //立即监听--进入就会执行一次 监听显影状态
  }
);
function onChange(e) {
  data.selectedValues = e.selectedValues;
  if (e.columnIndex === 0 || e.columnIndex === 1) {
    updateMonthAndDay(data.selectedValues[0], data.selectedValues[1]);
  }
}
function getcolumns() {
  let dateVaules;
  if (props.values != "") {
    let strtime = props.values; //传入的时间
    //console.log(strtime); 2023-09-05 19:28:00
    let date = new Date(strtime.replace(/-/g, "/"));
    // console.log(date); Wed Aug 09 2023 14:53:15 GMT+0800 (中国标准时间)
    let timeVaules = date.getTime();
    dateVaules = new Date(timeVaules);
  } else {
    dateVaules = new Date(); //没有传入时间则默认当前时刻
  }
  let Y = dateVaules.getFullYear();
  let M = dateVaules.getMonth();
  let D = dateVaules.getDate();
  let h = dateVaules.getHours();
  let m = dateVaules.getMinutes();
  let s = dateVaules.getSeconds();
  let year = []; //获取前后十年数组
  year.values = [];
  let Currentday = new Date().getFullYear();
  for (let i = Currentday - 10; i < Currentday + 10; i++) {
    year.push({ text: i.toString() + "年", value: i.toString() });
  }
  year.defaultIndex = year.values.indexOf(Y); //设置默认选项当前年

  // 个位数补0
  const _M = M < 9 ? `0${M + 1}` : (M + 1).toString(); //月份比实际获取的少1，所以要加1
  const _D = D < 10 ? `0${D}` : D.toString();
  const _h = h < 10 ? `0${h}` : h.toString();
  const _m = m < 10 ? `0${m}` : m.toString();
  const _s = s < 10 ? `0${s}` : s.toString();
  // 生成年月日时分秒时间值
  data.selectedValues.push(Y.toString());
  data.selectedValues.push(_M);
  data.selectedValues.push(_D);
  data.selectedValues.push(_h);
  data.selectedValues.push(_m);
  data.selectedValues.push(_s);
  data.columns.push(year); //生成年列

  let month = []; //获取12月数组
  month = Object.keys(Array.apply(null, { length: 13 })).map(function (item) {
    if (+item + 1 <= 10) {
      return { text: "0" + item + "月", value: "0" + item };
    } else if (+item + 1 == 11) {
      return { text: (+item).toString() + "月", value: (+item).toString() };
    } else {
      return {
        text: (+item + 0).toString() + "月",
        value: (+item + 0).toString(),
      };
    }
  });
  month.splice(0, 1);
  data.columns.push(month); //生成月列

  //获取当月的天数
  let days = getCountDays(Y, M + 1);
  let day = []; //创建当月天数数组
  day = Object.keys(Array.apply(null, { length: days + 1 })).map(function (
    item
  ) {
    if (+item + 1 <= 10) {
      return { text: "0" + item + "日", value: "0" + item };
    } else if (+item + 1 == 11) {
      return { text: (+item).toString() + "日", value: (+item).toString() };
    } else {
      return {
        text: (+item + 0).toString() + "日",
        value: (+item + 0).toString(),
      };
    }
  });
  day.splice(0, 1);
  data.columns.push(day); //生成日列

  let hour = []; //创建小时数组
  hour = Object.keys(Array.apply(null, { length: 24 })).map(function (item) {
    if (+item + 1 <= 10) {
      return { text: "0" + item + "时", value: "0" + item };
    } else if (+item + 1 == 11) {
      return { text: (+item).toString() + "时", value: (+item).toString() };
    } else {
      return {
        text: (+item + 0).toString() + "时",
        value: (+item + 0).toString(),
      };
    }
  });
  data.columns.push(hour); //生成小时列

  let mi = []; //创建分钟数组
  mi = Object.keys(Array.apply(null, { length: 60 })).map(function (item) {
    if (+item + 1 <= 10) {
      return { text: "0" + item + "分", value: "0" + item };
    } else if (+item + 1 == 11) {
      return { text: (+item).toString() + "分", value: (+item).toString() };
    } else {
      return {
        text: (+item + 0).toString() + "分",
        value: (+item + 0).toString(),
      };
    }
  });
  data.columns.push(mi); //生成分钟列

  let ss = []; //创建秒数数组
  ss = Object.keys(Array.apply(null, { length: 60 })).map(function (item) {
    if (+item + 1 <= 10) {
      return { text: "0" + item + "秒", value: "0" + item };
    } else if (+item + 1 == 11) {
      return { text: (+item).toString() + "秒", value: (+item).toString() };
    } else {
      return {
        text: (+item + 0).toString() + "秒",
        value: (+item + 0).toString(),
      };
    }
  });
  data.columns.push(ss); //生成秒钟列
}

function getCountDays(year, month) {
  //获取某年某月多少天
  let day = new Date(year, month, 0);
  return day.getDate();
}
// 更新月份和日期列
function updateMonthAndDay(year, month) {
  // 获取当前月份的天数
  const daysInMonth = getCountDays(year, month);
  // 更新日期列
  let day = [];
  day = Object.keys(Array.apply(null, { length: daysInMonth + 1 })).map(
    function (item) {
      if (+item + 1 <= 10) {
        return { text: "0" + item + "日", value: "0" + item };
      } else {
        return { text: (+item).toString() + "日", value: (+item).toString() };
      }
    }
  );
  day.splice(0, 1);

  // 更新日期列
  if (data.columns.length >= 3) {
    data.columns.splice(2, 1, day);
  } else {
    data.columns.push(day);
  }
}

/**
 * 显示弹框
 */
function showPicker() {
  data.isPicker = true;
  let momentDate;
  if (props.values != "") {
    momentDate = moment(props.values);
  } else {
    momentDate = moment();
  }

  const Y = momentDate.format("YYYY");
  const M = momentDate.format("MM"); // Already 1-based and zero-padded
  const D = momentDate.format("DD"); // Zero-padded
  const h = momentDate.format("HH"); // Zero-padded, 24-hour format
  const m = momentDate.format("mm"); // Zero-padded
  const s = momentDate.format("ss"); // Zero-padded

  // Clear and push new values
  data.selectedValues = [Y, M, D, h, m, s];
}

// 关闭弹框
function confirmOn() {
  data.isPicker = false;
}

//时间选择器关闭 值不改变并关闭弹框
function cancelOn({ selectedValues }) {
  confirmOn();
}

// 时间选择器确定 值改变
function onConfirm({ selectedValues }) {
  let endval =
    selectedValues[0] +
    "-" +
    selectedValues[1] +
    "-" +
    selectedValues[2] +
    " " +
    selectedValues[3] +
    ":" +
    selectedValues[4] +
    ":" +
    selectedValues[5];

  confirmOn();
  displayValue.value = endval
  emit("update:modelValue", endval);
}
</script>
