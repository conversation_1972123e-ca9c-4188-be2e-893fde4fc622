/**
 * 表单配置工具函数
 * 用于快速生成常用的表单字段配置
 */

/**
 * 创建输入框配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createInputField(options = {}) {
  return {
    type: 'input',
    name: options.name,
    label: options.label,
    placeholder: options.placeholder || `请输入${options.label}`,
    rules: options.rules || [],
    clearable: options.clearable !== false,
    readonly: options.readonly || false,
    disabled: options.disabled || false,
    inputType: options.inputType || 'text',
    maxlength: options.maxlength,
    showWordLimit: options.showWordLimit || false,
    defaultValue: options.defaultValue || '',
    ...options
  }
}

/**
 * 创建文本域配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createTextareaField(options = {}) {
  return {
    type: 'textarea',
    name: options.name,
    label: options.label,
    placeholder: options.placeholder || `请输入${options.label}`,
    rules: options.rules || [],
    rows: options.rows || 3,
    autosize: options.autosize || false,
    maxlength: options.maxlength,
    showWordLimit: options.showWordLimit || false,
    readonly: options.readonly || false,
    disabled: options.disabled || false,
    defaultValue: options.defaultValue || '',
    ...options
  }
}

/**
 * 创建选择器配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createSelectField(options = {}) {
  return {
    type: 'select',
    name: options.name,
    label: options.label,
    placeholder: options.placeholder || `请选择${options.label}`,
    rules: options.rules || [],
    options: options.options || [],
    defaultValue: options.defaultValue || '',
    ...options
  }
}

/**
 * 创建日期选择器配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createDateField(options = {}) {
  return {
    type: 'date',
    name: options.name,
    label: options.label,
    placeholder: options.placeholder || `请选择${options.label}`,
    rules: options.rules || [],
    dateType: options.dateType || 'date', // date, datetime, year-month
    minDate: options.minDate,
    maxDate: options.maxDate,
    defaultValue: options.defaultValue || '',
    ...options
  }
}

/**
 * 创建时间选择器配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createTimeField(options = {}) {
  return {
    type: 'time',
    name: options.name,
    label: options.label,
    placeholder: options.placeholder || `请选择${options.label}`,
    rules: options.rules || [],
    defaultValue: options.defaultValue || '',
    ...options
  }
}

/**
 * 创建数字输入框配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createNumberField(options = {}) {
  return {
    type: 'number',
    name: options.name,
    label: options.label,
    placeholder: options.placeholder || `请输入${options.label}`,
    rules: options.rules || [],
    min: options.min,
    max: options.max,
    step: options.step || 1,
    readonly: options.readonly || false,
    disabled: options.disabled || false,
    defaultValue: options.defaultValue || '',
    ...options
  }
}

/**
 * 创建开关配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createSwitchField(options = {}) {
  return {
    type: 'switch',
    name: options.name,
    label: options.label,
    disabled: options.disabled || false,
    defaultValue: options.defaultValue || false,
    ...options
  }
}

/**
 * 创建单选框组配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createRadioField(options = {}) {
  return {
    type: 'radio',
    name: options.name,
    label: options.label,
    options: options.options || [],
    direction: options.direction || 'horizontal',
    disabled: options.disabled || false,
    rules: options.rules || [],
    defaultValue: options.defaultValue || '',
    ...options
  }
}

/**
 * 创建复选框组配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createCheckboxField(options = {}) {
  return {
    type: 'checkbox',
    name: options.name,
    label: options.label,
    options: options.options || [],
    direction: options.direction || 'horizontal',
    disabled: options.disabled || false,
    rules: options.rules || [],
    defaultValue: options.defaultValue || [],
    ...options
  }
}

/**
 * 创建评分配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createRateField(options = {}) {
  return {
    type: 'rate',
    name: options.name,
    label: options.label,
    count: options.count || 5,
    size: options.size || 20,
    color: options.color || '#ffd21e',
    voidColor: options.voidColor || '#c8c9cc',
    disabled: options.disabled || false,
    readonly: options.readonly || false,
    defaultValue: options.defaultValue || 0,
    ...options
  }
}

/**
 * 创建滑块配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createSliderField(options = {}) {
  return {
    type: 'slider',
    name: options.name,
    label: options.label,
    min: options.min || 0,
    max: options.max || 100,
    step: options.step || 1,
    disabled: options.disabled || false,
    readonly: options.readonly || false,
    defaultValue: options.defaultValue || 0,
    ...options
  }
}

/**
 * 创建步进器配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createStepperField(options = {}) {
  return {
    type: 'stepper',
    name: options.name,
    label: options.label,
    min: options.min,
    max: options.max,
    step: options.step || 1,
    disabled: options.disabled || false,
    readonly: options.readonly || false,
    defaultValue: options.defaultValue || 0,
    ...options
  }
}

/**
 * 创建上传配置
 * @param {Object} options 配置选项
 * @returns {Object} 字段配置
 */
export function createUploadField(options = {}) {
  return {
    type: 'upload',
    name: options.name,
    label: options.label,
    maxCount: options.maxCount || 1,
    maxSize: options.maxSize,
    accept: options.accept,
    disabled: options.disabled || false,
    readonly: options.readonly || false,
    defaultValue: options.defaultValue || [],
    ...options
  }
}

/**
 * 常用验证规则
 */
export const commonRules = {
  // 必填
  required: (message) => ({ required: true, message: message || '此项为必填项' }),
  
  // 手机号
  phone: { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
  
  // 邮箱
  email: { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱地址' },
  
  // 身份证号
  idCard: { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号码' },
  
  // 数字
  number: { pattern: /^\d+$/, message: '请输入数字' },
  
  // 小数
  decimal: { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字' },
  
  // 最小长度
  minLength: (length) => ({ min: length, message: `最少输入${length}个字符` }),
  
  // 最大长度
  maxLength: (length) => ({ max: length, message: `最多输入${length}个字符` })
}

/**
 * 创建专家申请表单配置
 * @param {Object} enumOptions 枚举选项
 * @returns {Array} 表单配置数组
 */
export function createExpertApplyFormConfig(enumOptions = {}) {
  return [
    createInputField({
      name: 'name',
      label: '姓名',
      rules: [commonRules.required('请输入姓名')],
      maxlength: 20,
      showWordLimit: true
    }),
    createSelectField({
      name: 'gender',
      label: '性别',
      options: enumOptions.genderOptions || [],
      rules: [commonRules.required('请选择性别')]
    }),
    createInputField({
      name: 'idNum',
      label: '身份证号',
      rules: [
        commonRules.required('请输入身份证号'),
        commonRules.idCard
      ]
    }),
    createInputField({
      name: 'phone',
      label: '手机号',
      inputType: 'tel',
      rules: [
        commonRules.required('请输入手机号'),
        commonRules.phone
      ]
    }),
    createInputField({
      name: 'email',
      label: '邮箱',
      inputType: 'email',
      rules: [
        commonRules.required('请输入邮箱'),
        commonRules.email
      ]
    }),
    createSelectField({
      name: 'expertType',
      label: '专家类型',
      options: enumOptions.expertTypeOptions || [],
      rules: [commonRules.required('请选择专家类型')]
    }),
    createTextareaField({
      name: 'experience',
      label: '工作经历',
      rules: [commonRules.required('请输入工作经历')],
      maxlength: 500,
      showWordLimit: true
    })
  ]
}
