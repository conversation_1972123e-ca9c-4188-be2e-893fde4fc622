<!-- 通用列表组件 -->
<template>
  <div class="funi-list-page">
    <template v-for="item in tabsData" :key="item.key">
      <!-- 搜索栏 -->
      <div
        v-if="item.showSearch !== false && item.key == currentTabData.key"
        class="search-section"
      >
        <van-search
          v-model="item.searchKeyword"
          :placeholder="item.searchPlaceholder"
          @search="onSearch"
          @clear="onClear"
        />
        <div
          v-if="item.showFilter !== false"
          class="filter-icon"
          @click="item.showFilterPopup = true"
        >
          <van-icon name="wap-nav" />
          <span>筛选</span>
        </div>
        <!-- 自定义头部插槽 -->
        <div v-if="$slots.header" class="header-section">
          <slot :name="item.key + 'header'"></slot>
        </div>
      </div>
    </template>
    <!-- tabs切换 -->
    <van-tabs
      v-model:active="activeTab"
      @change="handleTabChange"
      :sticky="tabsSticky"
      :offset-top="tabsOffsetTop"
      :swipeable="tabsSwipeable"
      :animated="tabsAnimated"
      :show-header="(tabs.length == 1 ? false : true)"
    >
      <van-tab
        v-for="tab in tabs"
        :key="tab.key"
        :title="tab.title"
        :name="tab.key"
        :disabled="tab.disabled"
        :badge="tab.badge"
        :dot="tab.dot"
      >
        <!-- 列表内容 -->
        <div class="list-content">
          <van-list
            v-model:loading="currentTabData.loading"
            :finished="currentTabData.finished"
            :finished-text="currentTabData.listData.length ? finishedText : ''"
            @load="loadData"
          >
            <!-- 列表项插槽 -->
            <template
              v-for="(item, index) in currentTabData.listData"
              :key="getItemKey(item, index)"
            >
              <div class="list-item" @click="handleItemClick(item, index)">
                <slot
                  name="item"
                  :item="item"
                  :index="index"
                  :tab="getCurrentTab()"
                >
                  <!-- 默认列表项内容 -->
                  <div>自定义插槽</div>
                </slot>
              </div>
            </template>

            <!-- 空状态 -->
            <van-empty
              v-if="!currentTabData.listData.length && !currentTabData.loading"
              :description="emptyText"
              :image="emptyImage"
            />
          </van-list>
        </div>
      </van-tab>
    </van-tabs>

    <!-- 自定义底部插槽 -->
    <div v-if="$slots.footer" class="footer-section">
      <slot name="footer"></slot>
    </div>

    <!-- 筛选弹窗 -->
    <template v-for="item in tabsData" :key="item.key">
      <van-popup
        v-model:show="item.showFilterPopup"
        position="bottom"
        round
        :style="{ height: '60%' }"
      >
        <div class="filter-content">
          <div class="filter-header">
            <span class="filter-title">筛选条件</span>
            <van-icon
              name="cross"
              @click="item.showFilterPopup = false"
              class="close-icon"
            />
          </div>

          <div class="filter-body">
            <FuniForm
              ref="FuniFormRef"
              v-model="item.filterData"
              :form-config="item.formConfig"
              :show-submit-button="false"
              @submit="formSubmit"
            />
          </div>

          <!-- 筛选按钮 -->
          <div class="filter-actions">
            <van-button @click="btnReset" class="reset-btn" size="large"
              >重置</van-button
            >
            <van-button
              type="primary"
              @click="btnSubmit"
              class="apply-btn"
              size="large"
              >确定</van-button
            >
          </div>
        </div>
      </van-popup>
    </template>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { showToast } from "vant";
import FuniForm from "./FuniForm.vue";

// Props 定义
const props = defineProps({
  // 是否显示搜索栏
  showSearch: {
    type: Boolean,
    default: true,
  },
  // 搜索框占位符
  searchPlaceholder: {
    type: String,
    default: "请输入搜索关键词",
  },
  // 是否显示筛选按钮
  showFilter: {
    type: Boolean,
    default: true,
  },
  // 列表项的唯一标识字段
  itemKey: {
    type: String,
    default: "id",
  },
  // 分页大小
  pageSize: {
    type: Number,
    default: 20,
  },
  // 完成加载文本
  finishedText: {
    type: String,
    default: "没有更多了",
  },
  // 空状态文本
  emptyText: {
    type: String,
    default: "暂无数据",
  },
  // 空状态图片
  emptyImage: {
    type: String,
    default: "search",
  },
  // 是否自动加载
  autoLoad: {
    type: Boolean,
    default: true,
  },
  // 额外的查询参数
  extraParams: {
    type: Object,
    default: () => ({}),
  },
  // === Tabs 相关配置 ===
  // tabs 配置
  tabs: {
    type: Array,
    default: () => [],
    // 格式: [{ key: 'tab1', title: '标签1', loadFunction: fn, filterConfig: [], badge: '', dot: false, disabled: false }]
  },
  // 默认激活的 tab
  defaultActiveTab: {
    type: String,
    default: "",
  },
  // tabs 是否粘性定位
  tabsSticky: {
    type: Boolean,
    default: false,
  },
  // tabs 粘性定位偏移量
  tabsOffsetTop: {
    type: Number,
    default: 0,
  },
  // tabs 是否支持手势滑动
  tabsSwipeable: {
    type: Boolean,
    default: true,
  },
  // tabs 是否开启切换动画
  tabsAnimated: {
    type: Boolean,
    default: true,
  },
});

// Emits 定义
const emit = defineEmits([
  "item-click",
  "filter-change",
  "load-success",
  "load-error",
  "tab-change",
]);

const FuniFormRef = ref();

// 响应式数据
const loading = ref(false);
const finished = ref(false);
const listData = ref([]);
const filterData = ref({});

// Tabs 相关数据
const activeTab = ref("");
const tabsData = reactive({});

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: props.pageSize,
});


// 当前 tab 的数据
const currentTabData = computed(() => {
  const tabKey = activeTab.value;
  if (!tabsData[tabKey]) {
    tabsData[tabKey] = {
      loading: false,
      finished: false,
      listData: [],
      pagination: { page: 1, pageSize: props.pageSize },
    };
  }
  return tabsData[tabKey];
});

// 方法
const getItemKey = (item, index) => {
  return item[props.itemKey] || index;
};

const getCurrentTab = () => {
  return props.tabs.find((tab) => tab.key === activeTab.value);
};

// Tabs 切换处理
const handleTabChange = (tabKey) => {
  activeTab.value = tabKey;
  emit("tab-change", tabKey, getCurrentTab());

  // 如果该 tab 还没有数据，则加载数据
  if (!tabsData[tabKey] || tabsData[tabKey].listData.length === 0) {
    loadData();
  }
};

// 初始化 tabs
const initTabs = () => {
  if (props.tabs.length > 0) {
    // 设置默认激活的 tab
    activeTab.value = props.defaultActiveTab || props.tabs[0].key;

    // 初始化所有 tabs 的数据
    props.tabs.forEach((tab) => {
      tabsData[tab.key] = {
        key: tab.key,
        loading: false,
        finished: false,
        listData: [],
        searchPlaceholder: tab.searchPlaceholder || "请输入",
        searchKeyword: tab.searchKeyword || "",
        showFilterPopup: tab.showFilterPopup || false,
        formConfig: tab.filterConfig,
        filterData: tab.filterData,
        keyword:tab.keyword || 'keyword',
        pagination: { page: 1, pageSize: props.pageSize },
      };
    });
  }
};

const handleItemClick = (item, index) => {
  emit("item-click", item, index);
};

// 筛选表单提交事件
const formSubmit = (values) => {
  filterData.value = { ...values };
  tabsData[activeTab.value].showFilterPopup = false
  emit("filter-change", filterData.value);
  onSearch();
};

// 重置筛选
const btnReset = () => {
  let index =props.tabs.findIndex(x=>x.key == activeTab.value)
  filterData.value = {};
  if (FuniFormRef.value) {
    FuniFormRef.value[index].setValues({});
  }
};

// 提交筛选
const btnSubmit = () => {
  let index =props.tabs.findIndex(x=>x.key == activeTab.value)
  FuniFormRef.value[index].submit()
};

// 重置列表
const resetList = () => {
  const tabKey = activeTab.value;
    if (tabsData[tabKey]) {
      tabsData[tabKey].listData = [];
      tabsData[tabKey].pagination.page = 1;
      tabsData[tabKey].finished = false;
      tabsData[tabKey].loading = false;
    }
};

// 搜索
const onSearch = () => {
  resetList();
  loadData();
};

// 清空搜索
const onClear = () => {
  tabsData[activeTab.value].searchKeyword = "";
  onSearch();
};

/**
 * 过滤为空的数据
 * @param obj 
 */
function filterEmptyValues(obj) {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (value !== null && value !== undefined && value !== "" && !Number.isNaN(value) && value !== false) {
      acc[key] = value;
    }
    return acc;
  }, {});
}

// 加载数据
const loadData = async () => {
  let currentData, currentPagination, currentLoadFunction;
    const tabKey = activeTab.value;
    if (!tabsData[tabKey]) return;

    currentData = tabsData[tabKey];
    currentPagination = currentData.pagination;
    const currentTab = getCurrentTab();
    currentLoadFunction = currentTab?.loadFunction;

  if (currentData.loading || currentData.finished || !currentLoadFunction)
    return;

  try {
    currentData.loading = true;
    if (props.tabs.length === 0) loading.value = true;

    const params = {
      page: currentPagination.page,
      pageSize: currentPagination.pageSize,
      [currentData.keyword]: currentData.searchKeyword,
      ...filterData.value,
      ...props.extraParams,
    };

    // 如果是 tabs 模式，添加当前 tab 的额外参数
    if (props.tabs.length > 0) {
      const currentTab = getCurrentTab();
      if (currentTab?.extraParams) {
        Object.assign(params, currentTab.extraParams);
      }
    }

    // 调用加载函数
    const result = await currentLoadFunction(filterEmptyValues(params));

    // 处理返回结果
    let newData = [];
    let total = 0;

    if (Array.isArray(result)) {
      newData = result;
      total = result.length;
    } else if (result && typeof result === "object") {
      newData = result.data || result.list || result.items || [];
      total = result.total || result.count || newData.length;
    }

    // 追加数据
    currentData.listData.push(...newData);
    currentPagination.page++;

    // 判断是否还有更多数据
    if (
      currentData.listData.length >= total ||
      newData.length < currentPagination.pageSize
    ) {
      currentData.finished = true;
    }

    emit("load-success", result, currentData.listData);
  } catch (error) {
  } finally {
    currentData.loading = false;
  }
};

// 暴露方法
defineExpose({
  refresh: () => {
    resetList();
    loadData();
  },
  loadMore: loadData,
  resetFilter: () => {
    filterData.value = {};
    btnReset();
  },
  getListData: () => {
    if (props.tabs.length > 0) {
      const tabKey = activeTab.value;
      return tabsData[tabKey]?.listData || [];
    }
    return listData.value;
  },
  getFilterData: () => filterData.value,
  // Tabs 相关方法
  switchTab: (tabKey) => {
    if (props.tabs.find((tab) => tab.key === tabKey)) {
      handleTabChange(tabKey);
    }
  },
  getCurrentTab: () => getCurrentTab(),
  getActiveTab: () => activeTab.value,
  getTabData: (tabKey) => {
    return tabsData[tabKey] || null;
  },
  getAllTabsData: () => ({ ...tabsData }),
  refreshTab: (tabKey) => {
    if (tabKey) {
      const originalTab = activeTab.value;
      activeTab.value = tabKey;
      resetList();
      loadData();
      activeTab.value = originalTab;
    } else {
      resetList();
      loadData();
    }
  },
});

// 页面初始化
onMounted(() => {
  initTabs();
  if (props.autoLoad) {
    loadData();
  }
});
</script>

<style lang="scss" scoped>
.funi-list-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--van-background, #f7f8fa);
  ::v-deep() {
    .van-tabs {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    .van-tabs--line .van-tabs__wrap{
      flex-shrink: 0;
    }
    .van-swipe-item{
      overflow: scroll;
    }
  }
}

.search-section {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;

  :deep(.van-search) {
    flex: 1;
    padding: 0;
    background: #f7f8fa;

    .van-search__content {
      background: #f7f8fa;
      border-radius: 20px;
    }

    .van-field__control {
      font-size: 14px;
    }
  }

  .filter-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 12px;
    padding: 8px;
    cursor: pointer;

    .van-icon {
      font-size: 18px;
      color: #646566;
      margin-bottom: 2px;
    }

    span {
      font-size: 12px;
      color: #646566;
    }
  }
}

.header-section,
.footer-section,
.tabs-section {
  flex-shrink: 0;
}

.tabs-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  :deep(.van-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  :deep(.van-tabs__content) {
    flex: 1;
    overflow: hidden;
  }

  :deep(.van-tab__panel) {
    height: 100%;
    overflow: hidden;
  }
}

.list-content {
  height: 100%;
  overflow: scroll;
}

.list-item {
  background: white;
  margin: 8px 16px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.default-item {
  .item-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--van-text-color, #323233);
    margin-bottom: 8px;
    line-height: 1.4;
  }

  .item-content {
    font-size: 14px;
    color: var(--van-text-color-2, #646566);
    line-height: 1.5;
  }
}

.filter-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 16px 16px;
    border-bottom: 1px solid #f0f0f0;
    position: relative;

    .filter-title {
      font-size: 18px;
      font-weight: 600;
      color: #323233;
      text-align: center;
      flex: 1;
    }

    .close-icon {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 20px;
      color: #969799;
      cursor: pointer;
    }
  }

  .filter-body {
    flex: 1;
    padding: 0;
    overflow-y: auto;

    .van-form {
      padding: 0;
    }

    .van-cell {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }

    .van-field__label {
      width: 80px;
      color: #323233;
      font-size: 14px;
    }

    .van-field__control {
      color: #323233;
      font-size: 14px;
    }
  }
}

.filter-actions {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: white;

  .reset-btn {
    flex: 1;
    height: 48px;
    background: #f7f8fa;
    color: #646566;
    border: 1px solid #ebedf0;
    border-radius: 24px;
    font-size: 16px;
  }

  .apply-btn {
    flex: 2;
    height: 48px;
    background: #07c160;
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
