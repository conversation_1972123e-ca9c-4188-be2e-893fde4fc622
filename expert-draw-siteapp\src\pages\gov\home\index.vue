<!-- 管理端首页 -->
<template>
  <div class="gov-home-page">
    <!-- 头部绿色渐变区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-line">成都市农业农村局</div>
        <div class="subtitle-line">招标代理机构和评审专家抽取系统</div>
      </div>
      <div class="header-bg-pattern">
        <img src="@/assets/user/home/<USER>" alt="背景" class="bg-image" />
      </div>
    </div>

    <!-- 功能模块区域 -->
    <div class="function-modules">
      <div class="module-grid">
        <!-- 代理机构管理 -->
        <div class="module-card" @click="handleAgencyManagement">
          <div class="module-icon agency-icon">
            <img src="@/assets/store.png" alt="代理机构" />
          </div>
          <div class="module-text">代理机构</div>
        </div>

        <!-- 专家库管理 -->
        <div class="module-card" @click="handleExpertManagement">
          <div class="module-icon expert-icon">
            <img src="@/assets/operate.png" alt="专家管理" />
          </div>
          <div class="module-text">专家库管理</div>
        </div>

        <!-- 采购项目管理 -->
        <div class="module-card" @click="handleProjectManagement">
          <div class="module-icon project-icon">
            <img src="@/assets/plan.png" alt="采购项目" />
          </div>
          <div class="module-text">采购项目管理</div>
        </div>

        <!-- 名单管理 -->
        <div class="module-card" @click="handleListManagement">
          <div class="module-icon list-icon">
            <van-icon name="contact" size="32" color="white" />
          </div>
          <div class="module-text">名单管理</div>
        </div>
      </div>
    </div>

    <!-- 统计信息区域 -->
    <div class="stats-section" v-if="!loading">
      <van-cell-group>
        <van-cell title="代理机构总数" :value="homeStats.agencyCount || 0" />
        <van-cell title="专家总数" :value="homeStats.expertCount || 0" />
        <van-cell title="项目总数" :value="homeStats.projectCount || 0" />
        <van-cell title="今日抽取次数" :value="homeStats.todayDrawCount || 0" />
      </van-cell-group>
    </div>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>

    <!-- 底部信息 -->
    <div class="footer-info">
      <div class="contact-info">
        <div>管理端系统</div>
        <div>电话：028-61887395</div>
        <div>地址：成都市高新区天府软件园</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'

import { govHomeApi } from '@/api/home'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const homeStats = ref({})

// 方法
const fetchHomeData = async () => {
  try {
    loading.value = true
    // 获取管理端首页统计数据
    const stats = await govHomeApi.getGovHomeStats()
    homeStats.value = stats
  } catch (error) {
    console.error('获取管理端首页数据失败:', error)
    // 使用模拟数据
    homeStats.value = {
      agencyCount: 89,
      expertCount: 156,
      projectCount: 234,
      todayDrawCount: 12
    }
  } finally {
    loading.value = false
  }
}

const handleAgencyManagement = () => {
  router.push('/gov/agency')
}

const handleExpertManagement = () => {
  router.push('/gov/expert')
}

const handleProjectManagement = () => {
  router.push('/gov/procurement')
}

const handleListManagement = () => {
  router.push('/gov/draw-records')
}

// 生命周期
onMounted(() => {
  fetchHomeData()
})

onActivated(() => {
  fetchHomeData()
})
</script>

<style lang="scss" scoped>
.gov-home-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.header-section {
  position: relative;
  height: 200px;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.header-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.title-line {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  opacity: 0.9;
}

.subtitle-line {
  font-size: 22px;
  font-weight: bold;
  line-height: 1.2;
}

.header-bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;

  .bg-image {
    position: absolute;
    right: -20px;
    bottom: -20px;
    width: 200px;
    height: 200px;
    opacity: 0.3;
    object-fit: contain;
  }
}

.function-modules {
  padding: 24px 16px;
  background: white;
  margin-top: -20px;
  border-radius: 20px 20px 0 0;
  position: relative;
  z-index: 3;
}

.module-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.module-card {
  background: white;
  border-radius: 12px;
  padding: 24px 16px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15);
  }
}

.module-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  color: white;

  img {
    width: 32px;
    height: 32px;
    object-fit: contain;
  }

  &.agency-icon {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
  }

  &.expert-icon {
    background: linear-gradient(135deg, #2196F3, #1565C0);
  }

  &.project-icon {
    background: linear-gradient(135deg, #FF9800, #F57C00);
  }

  &.list-icon {
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  }
}

.module-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--van-text-color);
}

.stats-section {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.footer-info {
  margin-top: 40px;
  padding: 20px;
  text-align: center;
}

.contact-info {
  font-size: 12px;
  color: var(--van-text-color-2);
  line-height: 1.5;
}
</style>

<route lang="json5">
{
  name: 'GovHome',
  meta: {
    title: '招标代理机构和评审专家抽取系统'
  }
}
</route>
