<!-- 审批结果页面 -->
<template>
  <div class="result-page">
    <!-- 成功图标 -->
    <div class="success-icon">
      <van-icon name="checked" />
    </div>

    <!-- 标题 -->
    <div class="result-title">审批完成</div>

    <!-- 描述信息 -->
    <div class="result-description">
      已发送最新流程，请对任何一专家重复提<br>
      交规避数据！
    </div>

    <!-- 返回按钮 -->
    <div class="action-section">
      <van-button 
        type="primary" 
        class="back-button"
        @click="goBackToList"
      >
        返回项目申请记录
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回项目申请记录列表
const goBackToList = () => {
  router.replace('/user/project-records')
}

// 页面初始化时的处理
const handlePageInit = () => {
  // 可以在这里添加一些初始化逻辑
  // 比如记录审批完成的统计信息等
  console.log('审批结果页面初始化')
}

// 页面挂载时执行
onMounted(() => {
  handlePageInit()
})
</script>

<style lang="scss" scoped>
.result-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: #07c160;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  
  .van-icon {
    font-size: 40px;
    color: white;
  }
}

.result-title {
  font-size: 20px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
  line-height: 1.2;
}

.result-description {
  font-size: 14px;
  color: #969799;
  line-height: 1.5;
  margin-bottom: 60px;
  max-width: 300px;
}

.action-section {
  width: 100%;
  max-width: 300px;
  
  .back-button {
    width: 100%;
    height: 44px;
    background: #07c160;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .result-page {
    padding: 30px 16px;
  }
  
  .success-icon {
    width: 70px;
    height: 70px;
    margin-bottom: 24px;
    
    .van-icon {
      font-size: 35px;
    }
  }
  
  .result-title {
    font-size: 18px;
    margin-bottom: 12px;
  }
  
  .result-description {
    font-size: 13px;
    margin-bottom: 50px;
  }
}
</style>
