<!-- 项目申请记录查询页面 -->
<template>
  <div class="records-page">
    <FuniList
      ref="recordsListRef"
      :tabs="tabsConfig"
      :search-placeholder="searchPlaceholder"
      item-key="id"
      @item-click="handleItemClick"
      @tab-change="handleTabChange"
    >
      <template #INHeader>
        <van-notice-bar
          wrapable
           left-icon="info-o"
          :scrollable="false"
          text="完成抽取专家后，后台自动发送短信，需等待专家回复（15分钟超时）"
        />
      </template>
      <template #item="{ item, index }" @click="viewDetail(record)">
        <!-- 专家头像和基本信息 -->
        <div class="expert-header">
          <div class="expert-avatar">
            <van-icon name="contact" />
          </div>
          <div class="expert-basic">
            <div class="expert-name">{{ item.expert.name }}</div>
            <div class="expert-info">
              <span class="gender">{{
                getGenderText(item.expert.gender)
              }}</span>
              <span class="birthday">{{ item.expert.birthday }}</span>
              <span class="id-num">{{ item.expert.idNum }}</span>
            </div>
          </div>
        </div>
        <!-- 专家详细信息 -->
        <div class="expert-details">
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">专家类型</span>
              <span class="value">{{ item.expert.type?.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">单位职务</span>
              <span class="value">{{ item.expert.post }}</span>
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-item">
              <span class="label">所在领域</span>
              <span class="value">{{ item.expert.field }}</span>
            </div>
            <div class="detail-item">
              <span class="label">专业级别(技术职称)</span>
              <span class="value">{{ item.expert.level }}</span>
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-item">
              <span class="label">手机号</span>
              <span class="value">{{ item.expert.phone }}</span>
            </div>
            <div class="detail-item">
              <span class="label">座机号</span>
              <span class="value">{{ item.expert.landline }}</span>
            </div>
          </div>
        </div>
        <!-- 操作按钮 -->
        <div class="record-actions">
            <van-button size="small" type= plain block @click.stop="clickHande('evade',record)"> 专家规避 </van-button>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { showToast } from "vant";
import moment from "moment";
import { expertApi } from "@/api/expert";
import { useUserStore } from "@/stores";

const router = useRouter();
const route = useRoute();
const recordsListRef = ref();

const extras = reactive({
  IN: "",
  OUT: "",
  UN_CONFIRM: "",
  EVADE: "",
});

// Tabs 配置
const tabsConfig = computed(() => [
  {
    key: "IN",
    title: "入选",
    loadFunction: loadData,
    badge: extras.IN,
    showSearch:false,
    extraParams: {
      selectStatus: "IN",
    },
  },
  {
    key: "OUT",
    title: "淘汰",
    loadFunction: loadData,
    badge: extras.OUT,
    showSearch:false,
    extraParams: {
      selectStatus: "OUT",
    },
  },
  {
    key: "UN_CONFIRM",
    title: "待确认",
    loadFunction: loadData,
    badge: extras.UN_CONFIRM,
    showSearch:false,
    extraParams: {
      selectStatus: "UN_CONFIRM",
    },
  },
  {
    key: "EVADE",
    title: "规避",
    loadFunction: loadData,
    badge: extras.EVADE,
    showSearch:false,
    extraParams: {
      selectStatus: "EVADE",
    },
  },
]);

async function loadData(params) {
  const response = await expertApi.getReviewProject({ id: route.params.id });
  let data = response.data.drawResults.filter(
    (x) => x.selectStatus == params.selectStatus
  );
  for (const key in extras) {
    extras[key] = response.data.drawResults.filter(
      (x) => x.selectStatus == key
    ).length;
  }
  return {
    data: data,
    total: data.length,
  };
}

// 事件处理方法
const handleItemClick = (record, index) => {
  console.log("点击记录:", record, index);
};

const handleTabChange = (tabKey, tab) => {
  console.log("Tab 切换:", tabKey, tab);
};
// 获取性别文本
const getGenderText = (gender) => {
  return window.$enums.getEnumText("Gender", gender);
};

const clickHande = (type, record) => {
  switch (type) {
    case "evade":
      router.push(`/user/expert-apply?id=${record.entityId}`);
      break;
    case "progress":
      router.push(`/user/expert-apply/progress/${record.entityId}`);
      break;
  }
};
</script>

<style lang="scss" scoped>
.records-page {
  height: calc(100vh - 46px);
  background: #f5f5f5;
}
.expert-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .expert-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    .van-icon {
      font-size: 24px;
      color: #969799;
    }
  }

  .expert-basic {
    flex: 1;

    .expert-name {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 4px;
    }

    .expert-info {
      font-size: 12px;
      color: #646566;

      span {
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.expert-details {
  .detail-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .detail-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .value {
        font-size: 14px;
        color: #000;
        font-weight: 500;
        &:empty::after {
          content: "--";
        }
      }
    }
  }
}

.record-actions{
    padding-top:12px;
}
</style>

<route lang="json5">
{
  name: "projectList",
  meta: {
    title: "申请记录",
  },
}
</route>
