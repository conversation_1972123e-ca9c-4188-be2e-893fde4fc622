<!-- 代理机构列表页面 -->
<template>
  <div class="expertCategory-list-page">
    <FuniList
      ref="expertCategoryListRef"
      :tabs="tabsConfig"
      item-key="id"
      empty-text="暂无代理机构数据"
      @item-click="handleexpertCategoryClick"
    >
      <template #item="{ item: expertCategory }">
        <div class="expertCategory-card">
          <div class="expertCategory-header">
            <div class="expertCategory-name">{{ expertCategory.name }}</div>
            <div class="expertCategory-status" :class="expertCategory.enabled?'enabled':''">{{ expertCategory.enabled?'已启用':'已停用' }}</div>
          </div>

          <div class="expertCategory-info">
            <div class="info-row">
              <span class="label">人数</span>
              <span class="value">{{ expertCategory.plNumber }}</span>
            </div>
            <div class="info-row">
              <span class="label">创建时间</span>
              <span class="value">{{ expertCategory.createTime }}</span>
            </div>
          </div>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { expertApi } from "@/api/expert";

const router = useRouter();
const expertCategoryListRef = ref();

// 数据加载函数
const loadexpertCategoryData = async (params) => {
  const response = await expertApi.getExpertTypes(params);
  // 处理返回数据
  return {
    data: response.data || response.list || [],
    total: response.total || response.count || 0,
  };
};

// Tabs 配置
const tabsConfig = [
  {
    key: "one",
    title: "专家种类",
    loadFunction: loadexpertCategoryData,
    searchPlaceholder: "输入专家种类名称查找",
    keyword: "searchText",
    filterConfig: [
      {
        type: "select",
        label: "是否启用",
        prop: "enabled",
        options: [
          {
            text: "是",
            value: 'true',
          },
          {
            text: "否",
            value: 'false',
          },
        ],
      },
    ],
  },
];

// 事件处理方法
const handleexpertCategoryClick = (expertCategory, index) => {
  router.push(`/pub/expertCategory/detail?id=${expertCategory.id}`)
};

</script>

<style lang="scss" scoped>
.expertCategory-list-page {
  height: calc(100vh - 46px);
  background-color: var(--van-background);
}

.expertCategory-card {
}

.expertCategory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.expertCategory-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  flex: 1;
  margin-right: 12px;
}

.expertCategory-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: #FF91301A;
  color: #FF9130;

  &.enabled {
    background-color: var(--van-success-color);
    color: white;
  }
}

.expertCategory-info {
  .info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .label {
    color: var(--van-text-color-2);
    width: 80px;
    flex-shrink: 0;
  }

  .value {
    color: var(--van-text-color);
    flex: 1;
  }
}
</style>

<route lang="json5">
{
  name: "expertCategoryList",
  meta: {
    title: "代理机构列表",
  },
}
</route>
