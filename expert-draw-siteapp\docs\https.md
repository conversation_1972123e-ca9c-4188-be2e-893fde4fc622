# http请求
1.项目使用全局 `window.$http` 实例，基于 @funi-lib/utils：
2.HTTP 实例已配置统一错误处理 无需再单独处理
3.拦截器中已携带`keeper-auth`请求头
```javascript
// GET 请求
const response = await window.$http.fetch('/api/users', { page: 1 })

// POST 请求
const response = await window.$http.post('/api/users', userData)

// PUT 请求
const response = await window.$http.put('/api/users/1', userData)

// DELETE 请求
const response = await window.$http.delete('/api/users/1')
```

##  API 模块化组织
```javascript
// src/api/meeting.js
export const meetingApi = {
  /**
   * 获取待开会议列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回会议列表数据
   */
  getKeeperFromDataApp: (params) => {
    return window.$http.fetch('/conference/keeperFromDataApp', params)
  }
}
```