<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无预算项目前期工作抽取代理机构 - 演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .demo-container {
            width: 375px;
            height: 667px;
            background: #f5f5f5;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
        }
        
        .demo-info {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px;
            border-radius: 6px;
            font-size: 11px;
            text-align: center;
            z-index: 10;
        }
        
        .nav-bar {
            background: white;
            border-bottom: 1px solid #f7f8fa;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            margin-top: 30px;
        }
        
        .nav-back {
            font-size: 18px;
            color: #323233;
            margin-right: 12px;
            cursor: pointer;
        }
        
        .nav-title {
            font-size: 16px;
            font-weight: 600;
            color: #323233;
            flex: 1;
            text-align: center;
            margin-right: 30px;
        }
        
        .form-container {
            background: white;
            margin: 12px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .form-field {
            padding: 16px;
            border-bottom: 1px solid #f7f8fa;
            display: flex;
            align-items: center;
        }
        
        .form-field:last-child {
            border-bottom: none;
        }
        
        .field-label {
            color: #323233;
            font-weight: 500;
            font-size: 14px;
            width: 100px;
            flex-shrink: 0;
            position: relative;
        }
        
        .field-label.required::before {
            content: '*';
            color: #ee0a24;
            font-size: 14px;
            margin-right: 4px;
        }
        
        .field-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
            color: #323233;
            background: transparent;
        }
        
        .field-input::placeholder {
            color: #c8c9cc;
        }
        
        .field-input[readonly] {
            cursor: pointer;
        }
        
        .field-textarea {
            min-height: 60px;
            resize: none;
            font-family: inherit;
        }
        
        .field-arrow {
            color: #969799;
            font-size: 12px;
            margin-left: 8px;
        }
        
        .field-unit {
            color: #646566;
            font-size: 14px;
            margin-left: 8px;
        }
        
        .type-buttons {
            display: flex;
            gap: 12px;
            flex: 1;
        }
        
        .type-btn {
            flex: 1;
            padding: 8px 16px;
            border: 1px solid #ebedf0;
            border-radius: 4px;
            background: #f7f8fa;
            color: #646566;
            font-size: 14px;
            cursor: pointer;
            text-align: center;
        }
        
        .type-btn.active {
            background: #07c160;
            color: white;
            border-color: #07c160;
        }
        
        .budget-buttons {
            display: flex;
            gap: 12px;
            flex: 1;
        }
        
        .budget-btn {
            flex: 1;
            padding: 8px 16px;
            border: 1px solid #ebedf0;
            border-radius: 4px;
            background: #f7f8fa;
            color: #646566;
            font-size: 14px;
            cursor: pointer;
            text-align: center;
        }
        
        .budget-btn.active {
            background: #07c160;
            color: white;
            border-color: #07c160;
        }
        
        .submit-section {
            padding: 24px 16px;
            background: white;
            margin-top: 12px;
        }
        
        .submit-btn {
            width: 100%;
            height: 44px;
            background: #07c160;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            color: white;
            cursor: pointer;
        }
        
        .submit-btn:hover {
            background: #06ad56;
        }
        
        .submit-btn:active {
            background: #059748;
        }
        
        .form-content {
            height: calc(100% - 120px);
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-info">
            无预算项目前期工作抽取代理机构 - 375x667 移动端视图
        </div>
        
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-back">←</div>
            <div class="nav-title">无预算项目前期工作抽取代理机构</div>
        </div>

        <!-- 表单内容 -->
        <div class="form-content">
            <div class="form-container">
                <!-- 项目名称 -->
                <div class="form-field">
                    <div class="field-label required">项目名称</div>
                    <input type="text" class="field-input" placeholder="请输入">
                </div>

                <!-- 项目类型 -->
                <div class="form-field">
                    <div class="field-label required">项目类型</div>
                    <div class="type-buttons">
                        <div class="type-btn">机关</div>
                        <div class="type-btn active">直属单位</div>
                    </div>
                </div>

                <!-- 承办处室 -->
                <div class="form-field">
                    <div class="field-label required">承办处室</div>
                    <input type="text" class="field-input" value="成都市农业农村局办公室" readonly>
                </div>

                <!-- 采购内容(用途) -->
                <div class="form-field">
                    <div class="field-label">采购内容(用途)</div>
                    <textarea class="field-input field-textarea" placeholder="请输入"></textarea>
                </div>

                <!-- 预算分类 -->
                <div class="form-field">
                    <div class="field-label required">预算分类</div>
                    <div class="budget-buttons">
                        <div class="budget-btn">政府采购项目</div>
                        <div class="budget-btn active">非政府采购项目</div>
                    </div>
                </div>

                <!-- 采购品目 -->
                <div class="form-field">
                    <div class="field-label">采购品目</div>
                    <input type="text" class="field-input" placeholder="请输入">
                </div>

                <!-- 数量 -->
                <div class="form-field">
                    <div class="field-label">数量</div>
                    <input type="number" class="field-input" placeholder="请输入">
                </div>

                <!-- 经费预算 -->
                <div class="form-field">
                    <div class="field-label">经费预算</div>
                    <input type="number" class="field-input" placeholder="请输入">
                    <div class="field-unit">万元</div>
                </div>

                <!-- 立项时间 -->
                <div class="form-field">
                    <div class="field-label">立项时间</div>
                    <input type="text" class="field-input" placeholder="请选择" readonly>
                    <div class="field-arrow">></div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="submit-section">
                <button class="submit-btn">提交申请</button>
            </div>
        </div>
    </div>
    
    <script>
        console.log('无预算项目前期工作抽取代理机构页面演示加载完成');
        
        // 模拟交互
        document.querySelector('.submit-btn').addEventListener('click', function() {
            alert('提交申请功能演示');
        });
        
        document.querySelector('.nav-back').addEventListener('click', function() {
            alert('返回上一页');
        });
        
        // 项目类型切换
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.type-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 预算分类切换
        document.querySelectorAll('.budget-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.budget-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 模拟选择器（排除承办处室）
        document.querySelectorAll('input[readonly]').forEach(input => {
            const label = input.parentElement.querySelector('.field-label').textContent;
            if (label !== '承办处室') {
                input.addEventListener('click', function() {
                    alert(`选择${label}功能演示`);
                });
            }
        });
    </script>
</body>
</html>
