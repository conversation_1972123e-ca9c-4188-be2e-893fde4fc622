/**
 * 用户状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)

  // 计算属性
  const isLoggedIn = computed(() => !!userInfo.value)
  
  // 获取用户姓名
  const userName = computed(() => userInfo.value?.nickname || '')
  
  // 获取用户部门信息
  const userDepartment = computed(() => {
    if (!userInfo.value?.groups || userInfo.value.groups.length === 0) {
      return null
    }
    // 返回第一个部门信息
    return userInfo.value.groups[0]
  })
  
  // 获取用户部门名称
  const departmentName = computed(() => userDepartment.value?.name || '农业农村局')
  
  // 获取用户角色
  const userRoles = computed(() => userInfo.value?.roles || [])
  
  // 获取用户权限
  const userAuths = computed(() => userInfo.value?.auths || [])

  // 方法
  /**
   * 获取当前用户信息
   */
  const fetchUserInfo = async () => {
    try {
      const response = await window.$http.fetch('/api/v1/current-user')
      userInfo.value = response.data
      return response.data
    } catch (error) {

    } finally {

    }
  }

  /**
   * 清除用户信息
   */
  const clearUserInfo = () => {
    userInfo.value = null
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
  }

  /**
   * 检查用户是否有指定角色
   */
  const hasRole = (roleCode) => {
    return userRoles.value.some(role => role.code === roleCode)
  }

  /**
   * 检查用户是否属于指定部门类型
   */
  const isDepartmentType = (type) => {
    return userDepartment.value?.type === type
  }

  /**
   * 获取用户所在区域信息
   */
  const getUserArea = () => {
    return userDepartment.value?.area || null
  }

  return {
    // 状态
    userInfo,
    // 计算属性
    isLoggedIn,
    userName,
    userDepartment,
    departmentName,
    userRoles,
    userAuths,
    
    // 方法
    fetchUserInfo,
    clearUserInfo,
    updateUserInfo,
    hasRole,
    isDepartmentType,
    getUserArea
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage,
  }
})
