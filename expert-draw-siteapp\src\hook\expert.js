import { ref } from 'vue'
import { expertApi } from '@/api/expert.js'

/**
 * 
 * @returns 专家相关
 */
export default function useExpert() {
    //专家类型
    let expertTypes = ref([])
    expertApi.getExpertTypes().then(res => {
        res.data.forEach(item=>{
            item.text=item.name;
            item.value = item.id
        })
        expertTypes.value = res.data
    })

    /**
     * 获取专家列表
     */
    function getExpertList(params){
        return expertApi.getExpertList(params)
    }
    return {
        expertTypes,// 专家类型
        getExpertList
    }

}