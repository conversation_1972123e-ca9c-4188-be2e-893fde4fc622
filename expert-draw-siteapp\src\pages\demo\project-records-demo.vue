<!-- 申请记录列表示例 -->
<template>
  <div class="project-records-demo">
    <van-nav-bar title="申请记录示例" left-arrow @click-left="$router.back()" />
    
    <FuniList
      ref="recordsListRef"
      :tabs="tabsConfig"
      search-placeholder="搜索项目名称/编号"
      item-key="id"
      @item-click="handleItemClick"
      @tab-change="handleTabChange"
    >
      <template #item="{ item: record, index }">
        <div class="record-item">
          <!-- 项目标题 -->
          <div class="record-title">
            <div class="item-title">{{ record.name || "项目名称" }}</div>
            <span
              class="status-badge"
              :class="getStatusClass(record.flowStatus)"
            >
              {{ getStatusText(record.flowStatus) }}
            </span>
          </div>

          <!-- 项目信息 -->
          <div class="record-info">
            <div class="info-row">
              <span class="label">项目类型</span>
              <span class="value">{{ getProjectType(record.type) }}</span>
            </div>
            <div class="info-row">
              <span class="label">申请人</span>
              <span class="value">{{ record.applyUserName || "-" }}</span>
            </div>
            <div class="info-row">
              <span class="label">经办人处室</span>
              <span class="value">{{ record.department || "-" }}</span>
            </div>
            <div class="info-row">
              <span class="label">经办时间</span>
              <span class="value">{{ formatDateTime(record.flowTime || record.createTime) }}</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="record-actions">
            <van-button size="small" plain @click.stop="viewDetail(record)">
              详情
            </van-button>
            <van-button
              size="small"
              type="success"
              @click.stop="handleAction(record)"
            >
              {{ getActionText(record.flowStatus) }}
            </van-button>
          </div>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";
import moment from "moment";
import FuniList from "@/components/FuniList.vue";

const router = useRouter();
const recordsListRef = ref();

// Tabs 配置
const tabsConfig = computed(() => [
  {
    key: "APPLY",
    title: "已发起",
    loadFunction: loadApplyRecords,
    filterConfig: getFilterConfig(),
    extraParams: { flowStatus: "APPLY" }
  },
  {
    key: "TODO", 
    title: "待审批",
    loadFunction: loadTodoRecords,
    filterConfig: getFilterConfig(),
    extraParams: { flowStatus: "TODO" }
  },
  {
    key: "DONE",
    title: "已审批", 
    loadFunction: loadDoneRecords,
    filterConfig: getFilterConfig(),
    extraParams: { flowStatus: "DONE" }
  }
]);

// 筛选配置
const getFilterConfig = () => [
  {
    type: 'input',
    label: '项目名称',
    prop: 'name',
    props: {
      placeholder: '请输入项目名称',
      clearable: true
    }
  },
  {
    type: 'select',
    label: '项目类型',
    prop: 'type',
    options: [
      { text: '全部', value: '' },
      { text: '专家申请', value: 'EXPERT' },
      { text: '机构申请', value: 'AGENCY' }
    ],
    props: {
      placeholder: '请选择项目类型'
    }
  }
];

// 模拟数据生成
const generateMockData = (status, page = 1, pageSize = 20) => {
  const statusNames = {
    'APPLY': '已发起',
    'TODO': '待审批', 
    'DONE': '已审批'
  };
  
  return Array.from({ length: pageSize }, (_, index) => ({
    id: `${status}_${page}_${index}`,
    entityId: `entity_${status}_${page}_${index}`,
    name: `${statusNames[status]}项目 ${page}-${index + 1}`,
    type: index % 2 === 0 ? 'EXPERT' : 'AGENCY',
    flowStatus: status,
    applyUserName: `申请人${index + 1}`,
    department: `处室${index % 3 + 1}`,
    createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    flowTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
  }));
};

// 数据加载函数
async function loadApplyRecords(params) {
  console.log('加载已发起记录:', params);
  
  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const mockData = generateMockData('APPLY', params.page, params.pageSize);
  
  return {
    data: mockData,
    total: 100
  };
}

async function loadTodoRecords(params) {
  console.log('加载待审批记录:', params);
  
  await new Promise(resolve => setTimeout(resolve, 600));
  
  const mockData = generateMockData('TODO', params.page, params.pageSize);
  
  return {
    data: mockData,
    total: 80
  };
}

async function loadDoneRecords(params) {
  console.log('加载已审批记录:', params);
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const mockData = generateMockData('DONE', params.page, params.pageSize);
  
  return {
    data: mockData,
    total: 120
  };
}

// 事件处理方法
const handleItemClick = (record, index) => {
  console.log('点击记录:', record, index);
  showToast(`点击了: ${record.name}`);
};

const handleTabChange = (tabKey, tab) => {
  console.log('Tab 切换:', tabKey, tab);
  showToast(`切换到: ${tab.title}`);
};

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';
  return moment(dateTime).format('MM-DD HH:mm');
};

const getProjectType = (type) => {
  const typeMap = {
    'EXPERT': '专家申请',
    'AGENCY': '机构申请'
  };
  return typeMap[type] || type || '-';
};

const getStatusText = (status) => {
  const statusMap = {
    'APPLY': '已发起',
    'TODO': '待审批',
    'DONE': '已审批'
  };
  return statusMap[status] || status || '-';
};

const getStatusClass = (status) => {
  const classMap = {
    'APPLY': 'status-apply',
    'TODO': 'status-todo',
    'DONE': 'status-done'
  };
  return classMap[status] || '';
};

const getActionText = (status) => {
  const actionMap = {
    'APPLY': '查看进度',
    'TODO': '查看进度',
    'DONE': '查看结果'
  };
  return actionMap[status] || '查看详情';
};

const viewDetail = (record) => {
  showToast(`查看详情: ${record.name}`);
};

const handleAction = (record) => {
  showToast(`执行操作: ${getActionText(record.flowStatus)}`);
};
</script>

<style lang="scss" scoped>
.project-records-demo {
  height: 100vh;
  background: #f5f5f5;
}

.record-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.record-title {
  font-size: 15px;
  font-weight: 500;
  color: #323233;
  line-height: 1.4;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  
  .item-title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
  }
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  margin-left: 8px;
  flex-shrink: 0;

  &.status-apply {
    background: #e6f7ff;
    color: #1890ff;
  }

  &.status-todo {
    background: #fff7e6;
    color: #fa8c16;
  }

  &.status-done {
    background: #f6ffed;
    color: #52c41a;
  }
}

.record-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  color: #646566;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #323233;
  flex: 1;
}

.record-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;

  .van-button {
    border-radius: 16px;
    font-size: 12px;
    padding: 0 12px;
    height: 28px;
  }
}
</style>

<route lang="json5">
{
  name: 'ProjectRecordsDemo',
  meta: {
    title: '申请记录示例'
  }
}
</route>
