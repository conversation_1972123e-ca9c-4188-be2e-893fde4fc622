<!-- 专家种类详情页面 -->
<template>
  <div class="expert-detail-page" v-loading="loading">
    <!-- 专家种类详情内容 -->
      <van-cell-group>
            <van-cell title="专家种类:" :value="expertInfo.name || '--'" />
            <van-cell title="人数:" :value="expertInfo.plNumber || '--'" />
            <van-cell title="数据创建时间:" :value="expertInfo.createTime || '--'" />
            <van-cell title="备注:" :value="expertInfo.description || '--'" />
      </van-cell-group>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { expertApi } from '@/api/expert'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const expertInfo = ref({})

// 加载专家详情
const loadExpertDetail = async () => {
  try {
    loading.value = true
    // 调用API获取专家详情
    const response = await expertApi.getExpertTypeDetail(route.query.id)
    expertInfo.value = response.data
    
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadExpertDetail()
})
</script>

<style lang="scss" scoped>
.expert-detail-page {
  min-height: calc(100vh - 46px);
  padding-top: 12px;
  ::v-deep(){
    .van-cell__title{
      color:var(van-cell-value-color);
    }
    .van-cell__value{
      color:#000;
    }
  }
}

</style>
<route lang="json5">
{
  name: "expertCategoryDetail",
  meta: {
    title: "专家种类详情",
  },
}
</route>