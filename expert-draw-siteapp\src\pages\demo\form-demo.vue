<!-- 配置化表单使用示例 -->
<template>
  <div class="form-demo-page">
    <van-nav-bar title="配置化表单示例" left-arrow @click-left="$router.back()" />
    
    <div class="demo-section">
      <h3>基础表单示例</h3>
      <ConfigForm
        v-model="basicFormData"
        :form-config="basicFormConfig"
        :submit-loading="submitLoading"
        @submit="handleBasicSubmit"
        @failed="handleFormFailed"
      />
    </div>
    
    <div class="demo-section">
      <h3>复杂表单示例</h3>
      <ConfigForm
        v-model="complexFormData"
        :form-config="complexFormConfig"
        :submit-loading="submitLoading"
        submit-text="保存数据"
        @submit="handleComplexSubmit"
        @failed="handleFormFailed"
      />
    </div>
    
    <div class="demo-section">
      <h3>表单数据</h3>
      <van-cell-group>
        <van-cell title="基础表单数据" :value="JSON.stringify(basicFormData)" />
        <van-cell title="复杂表单数据" :value="JSON.stringify(complexFormData)" />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast, showSuccessToast } from 'vant'
import ConfigForm from '@/components/common/ConfigForm.vue'
import { useExpertTypes, useGender } from '@/composables/useEnums'

// 使用枚举数据
const { options: expertTypeOptions } = useExpertTypes()
const { options: genderOptions } = useGender()

// 响应式数据
const submitLoading = ref(false)
const basicFormData = reactive({})
const complexFormData = reactive({})

// 基础表单配置
const basicFormConfig = [
  {
    type: 'input',
    label: '姓名',
    prop: 'name',
    props: {
      placeholder: '请输入姓名',
      rules: [{ required: true, message: '请输入姓名' }],
      maxlength: 20,
      showWordLimit: true
    }
  },
  {
    type: 'select',
    label: '性别',
    prop: 'gender',
    options: genderOptions.value,
    props: {
      placeholder: '请选择性别',
      rules: [{ required: true, message: '请选择性别' }]
    }
  },
  {
    type: 'number',
    label: '年龄',
    prop: 'age',
    props: {
      placeholder: '请输入年龄',
      min: 1,
      max: 120,
      rules: [
        { required: true, message: '请输入年龄' },
        { pattern: /^\d+$/, message: '请输入有效的年龄' }
      ]
    }
  },
  {
    type: 'input',
    label: '手机号',
    prop: 'phone',
    props: {
      placeholder: '请输入手机号',
      type: 'tel',
      rules: [
        { required: true, message: '请输入手机号' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
      ]
    }
  },
  {
    type: 'textarea',
    label: '备注',
    prop: 'remark',
    props: {
      placeholder: '请输入备注信息',
      rows: 3,
      maxlength: 200,
      showWordLimit: true
    }
  }
]

// 复杂表单配置
const complexFormConfig = [
  {
    type: 'input',
    label: '专家姓名',
    prop: 'expertName',
    props: {
      placeholder: '请输入专家姓名',
      rules: [{ required: true, message: '请输入专家姓名' }]
    }
  },
  {
    type: 'select',
    label: '专家类型',
    prop: 'expertType',
    options: expertTypeOptions.value,
    props: {
      placeholder: '请选择专家类型',
      rules: [{ required: true, message: '请选择专家类型' }]
    }
  },
  {
    type: 'date',
    label: '出生日期',
    prop: 'birthDate',
    props: {
      placeholder: '请选择出生日期',
      dateType: 'date',
      maxDate: new Date(),
      rules: [{ required: true, message: '请选择出生日期' }]
    }
  },
  {
    type: 'time',
    label: '工作时间',
    prop: 'workTime',
    props: {
      placeholder: '请选择工作时间'
    }
  },
  {
    type: 'radio',
    label: '学历',
    prop: 'education',
    options: [
      { text: '本科', value: 'bachelor' },
      { text: '硕士', value: 'master' },
      { text: '博士', value: 'doctor' }
    ],
    props: {
      direction: 'horizontal',
      rules: [{ required: true, message: '请选择学历' }]
    }
  },
  {
    type: 'checkbox',
    label: '技能',
    prop: 'skills',
    options: [
      { text: 'JavaScript', value: 'js' },
      { text: 'Vue.js', value: 'vue' },
      { text: 'React', value: 'react' },
      { text: 'Node.js', value: 'node' }
    ],
    props: {
      direction: 'horizontal'
    }
  },
  {
    type: 'switch',
    label: '是否激活',
    prop: 'isActive',
    props: {
      defaultValue: true
    }
  },
  {
    type: 'rate',
    label: '评分',
    prop: 'rating',
    props: {
      count: 5,
      size: 24,
      color: '#ffd21e'
    }
  },
  {
    type: 'slider',
    label: '工作经验',
    prop: 'experience',
    props: {
      min: 0,
      max: 30,
      step: 1,
      defaultValue: 5
    }
  },
  {
    type: 'stepper',
    label: '项目数量',
    prop: 'projectCount',
    props: {
      min: 0,
      max: 100,
      step: 1,
      defaultValue: 1
    }
  },
  {
    type: 'upload',
    label: '头像',
    prop: 'avatar',
    props: {
      maxCount: 1,
      maxSize: 2 * 1024 * 1024, // 2MB
      accept: 'image/*'
    }
  }
]

// 处理基础表单提交
const handleBasicSubmit = async (values) => {
  try {
    submitLoading.value = true
    console.log('基础表单提交:', values)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showSuccessToast('基础表单提交成功')
  } catch (error) {
    console.error('基础表单提交失败:', error)
    showToast('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

// 处理复杂表单提交
const handleComplexSubmit = async (values) => {
  try {
    submitLoading.value = true
    console.log('复杂表单提交:', values)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showSuccessToast('复杂表单提交成功')
  } catch (error) {
    console.error('复杂表单提交失败:', error)
    showToast('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

// 处理表单验证失败
const handleFormFailed = (errorInfo) => {
  console.log('表单验证失败:', errorInfo)
  showToast('请检查表单填写')
}
</script>

<style lang="scss" scoped>
.form-demo-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.demo-section {
  margin: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--van-text-color);
  }
}
</style>

<route lang="json5">
{
  name: 'FormDemo',
  meta: {
    title: '配置化表单示例'
  }
}
</route>
