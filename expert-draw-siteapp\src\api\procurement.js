/**
 * 采购管理相关API
 */

export const procurementApi = {
  /**
   * 获取采购项目列表
   * @param {Object} params - 查询参数
   * @param {number} [params.page] - 页码
   * @param {number} [params.pageSize] - 每页条数
   * @param {string} [params.keyword] - 搜索关键词
   * @returns {Promise} 返回采购项目列表数据
   */
  getProjectList: (params = {}) => {
    return window.$http.fetch('/api/v1/procurement/projects', params)
  },

  /**
   * 获取采购项目详情
   * @param {string} id - 项目ID
   * @returns {Promise} 返回采购项目详情
   */
  getProjectDetail: (id) => {
    return window.$http.fetch(`/api/v1/procurement/projects/${id}`)
  },

  /**
   * 创建采购项目
   * @param {Object} data - 项目数据
   * @returns {Promise} 返回创建结果
   */
  createProject: (data) => {
    return window.$http.post('/api/v1/procurement/projects', data)
  },

  /**
   * 更新采购项目
   * @param {string} id - 项目ID
   * @param {Object} data - 项目数据
   * @returns {Promise} 返回更新结果
   */
  updateProject: (id, data) => {
    return window.$http.put(`/api/v1/procurement/projects/${id}`, data)
  },

  /**
   * 删除采购项目
   * @param {string} id - 项目ID
   * @returns {Promise} 返回删除结果
   */
  deleteProject: (id) => {
    return window.$http.delete(`/api/v1/procurement/projects/${id}`)
  },

  /**
   * 提交采购项目
   * @param {string} id - 项目ID
   * @returns {Promise} 返回提交结果
   */
  submitProject: (id) => {
    return window.$http.post(`/api/v1/procurement/projects/${id}/submit`)
  },

  /**
   * 审批采购项目
   * @param {string} id - 项目ID
   * @param {Object} data - 审批数据
   * @param {boolean} data.approved - 是否通过
   * @param {string} [data.reason] - 审批意见
   * @returns {Promise} 返回审批结果
   */
  approveProject: (id, data) => {
    return window.$http.post(`/api/v1/procurement/projects/${id}/approve`, data)
  },

  /**
   * 撤回采购项目
   * @param {string} id - 项目ID
   * @returns {Promise} 返回撤回结果
   */
  revokeProject: (id) => {
    return window.$http.post(`/api/v1/procurement/projects/${id}/revoke`)
  },

  /**
   * 获取采购分类列表
   * @returns {Promise} 返回采购分类列表
   */
  getCategories: () => {
    return window.$http.fetch('/api/v1/procurement/categories')
  },

  /**
   * 获取采购单位列表
   * @returns {Promise} 返回采购单位列表
   */
  getProcuringEntities: () => {
    return window.$http.fetch('/api/v1/procurement/entities')
  }
}
