<!-- 专家种类详情页面 -->
<template>
  <div class="category-detail-page">
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>

    <div v-else-if="categoryDetail" class="detail-content">
      <!-- 专家种类标题 -->
      <div class="category-header">
        <div class="category-title">专家种类</div>
        <div class="category-name">{{ categoryDetail.name }}</div>
      </div>

      <!-- 详情信息 -->
      <div class="detail-info">
        <div class="info-item">
          <div class="info-label">人数</div>
          <div class="info-value">{{ categoryDetail.plNumber || 0 }}</div>
        </div>

        <div class="info-item">
          <div class="info-label">数据创建时间</div>
          <div class="info-value">{{ formatDateTime(categoryDetail.createTime) }}</div>
        </div>

        <div class="info-item description-item">
          <div class="info-label">备注</div>
          <div class="info-value description-text">
            {{ categoryDetail.description || '暂无备注信息' }}
          </div>
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="action-section">
        <van-button
          type="default"
          size="large"
          block
          @click="handleBack"
          class="back-button"
        >
          返回
        </van-button>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-else
      description="专家种类不存在"
      image="error"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { expertApi } from '@/api/expert'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const statusLoading = ref(false)
const deleteLoading = ref(false)
const categoryDetail = ref(null)

// 方法
const fetchCategoryDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id

    // 调用获取专家种类详情的API
    const response = await expertApi.getExpertTypeDetail(id)
    categoryDetail.value = response

  } catch (error) {
    console.error('获取专家种类详情失败:', error)
    showToast('获取详情失败')

    // 使用模拟数据
    categoryDetail.value = {
      id: id,
      name: '财务审计类',
      description: '这里是备注信息，这里是备注信息这里是备注信息这里是备注信息这里是备注信息',
      plNumber: '59',
      enabled: 'true',
      createTime: '2025-12-12 12:00:23'
    }
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  router.back()
}



const formatDateTime = (dateString) => {
  if (!dateString) return ''
  // 如果已经是格式化的字符串，直接返回
  if (dateString.includes('-') && dateString.includes(':')) {
    return dateString
  }
  // 否则格式化日期
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

// 生命周期
onMounted(() => {
  fetchCategoryDetail()
})

onActivated(() => {
  fetchCategoryDetail()
})
</script>

<style lang="scss" scoped>
.category-detail-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.category-header {
  padding: 20px 16px;
  border-bottom: 1px solid var(--van-border-color);
}

.category-title {
  font-size: 14px;
  color: var(--van-text-color-2);
  margin-bottom: 8px;
}

.category-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--van-text-color);
}

.detail-info {
  padding: 16px;
}

.info-item {
  display: flex;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  &.description-item {
    flex-direction: column;
    align-items: flex-start;
  }
}

.info-label {
  color: var(--van-text-color-2);
  font-size: 14px;
  width: 120px;
  flex-shrink: 0;

  .description-item & {
    margin-bottom: 8px;
    width: auto;
  }
}

.info-value {
  color: var(--van-text-color);
  font-size: 14px;
  flex: 1;
  text-align: right;

  .description-item & {
    text-align: left;
  }
}

.description-text {
  line-height: 1.6;
  color: var(--van-text-color-2);
}

.action-section {
  margin-top: 40px;
  padding: 0 16px;
}

.back-button {
  background-color: var(--van-background-2);
  border: 1px solid var(--van-border-color);
  color: var(--van-text-color);
}
</style>

<route lang="json5">
{
  name: 'ExpertCategoryDetail',
  meta: {
    title: '专家种类详情'
  }
}
</route>
