import { createApp } from "vue";
import "./style.css";
import App from "./App.vue";
import router from "@/router";
import pinia from "@/stores";
import Vant from "vant";
import "vant/lib/index.css";
import "./style/theme.css";
import http from "@/utils/http";
import { setupDirectives } from "@/directives";
import useEnums from "@/hook/enums.js";

//请求挂载到全局
window.$http = http;

window.$enums = useEnums();
// 应用初始化函数
async function initApp() {
  try {
    // 初始化枚举数据
    await window.$enums.init();
  } finally {
    // 创建Vue应用
    const app = createApp(App);

    app.use(router);
    app.use(pinia);
    app.use(Vant);

    // 注册自定义指令
    setupDirectives(app);
    app.mount("#app");
  }
}

// 启动应用
initApp();
