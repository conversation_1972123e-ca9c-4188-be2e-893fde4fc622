{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@funi-lib/utils": "^0.1.4", "echarts": "^5.6.0", "moment": "^2.30.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "unplugin-vue-router": "^0.14.0", "vant": "^4.9.20", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "postcss-mobile-forever": "^5.0.0", "sass-embedded": "^1.89.2", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.3"}}