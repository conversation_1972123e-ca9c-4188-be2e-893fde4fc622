<!-- 无预算项目前期工作抽取代理机构 -->
<template>
  <div class="agency-add-page">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="无预算项目前期工作抽取代理机构"
      left-arrow
      @click-left="onBack"
    />

    <!-- 表单内容 -->
    <div class="form-container">
      <van-form @submit="onSubmit">
        <!-- 项目名称 -->
        <van-field
          v-model="formData.name"
          name="name"
          label="项目名称"
          placeholder="请输入"
          required
          :rules="[{ required: true, message: '请输入项目名称' }]"
        >
        </van-field>

        <!-- 项目类型 -->
        <van-field
          v-model="formData.typeText"
          name="type"
          label="项目类型"
          placeholder="请选择"
          readonly
          required
          is-link
          @click="showTypePicker = true"
          :rules="[{ required: true, message: '请选择项目类型' }]"
        >
        </van-field>

        <!-- 承办处室 -->
        <van-field
          v-model="formData.departmentText"
          name="department"
          label="承办处室"
          readonly
          required
          :rules="[{ required: true, message: '承办处室信息获取失败' }]"
        >
        </van-field>

        <!-- 采购内容(用途) -->
        <van-field
          v-model="formData.purpose"
          name="purpose"
          label="采购内容(用途)"
          placeholder="请输入"
          type="textarea"
          rows="3"
          autosize
        />

        <!-- 预算分类 -->
        <van-field
          v-model="formData.planTypeText"
          name="planType"
          label="预算分类"
          placeholder="请选择"
          readonly
          required
          is-link
          @click="showPlanTypePicker = true"
          :rules="[{ required: true, message: '请选择预算分类' }]"
        >
        </van-field>

        <!-- 采购品目 -->
        <van-field
          v-model="formData.items"
          name="items"
          label="采购品目"
          placeholder="请输入"
        />

        <!-- 数量 -->
        <van-field
          v-model="formData.number"
          name="number"
          label="数量"
          placeholder="请输入"
          type="number"
        />

        <!-- 经费预算 -->
        <van-field
          v-model="formData.fundsBudget"
          name="fundsBudget"
          label="经费预算"
          placeholder="请输入"
          type="number"
        >
          <template #right-icon>
            <span class="unit-text">万元</span>
          </template>
        </van-field>

        <!-- 立项时间 -->
        <van-field
          v-model="formData.projectDate"
          name="projectDate"
          label="立项时间"
          placeholder="请选择"
          readonly
          is-link
          @click="showDatePicker = true"
        />

        <!-- 提交按钮 -->
        <div class="submit-section">
          <van-button
            type="primary"
            native-type="submit"
            block
            :loading="submitting"
            class="submit-btn"
          >
            提交申请
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 项目类型选择器 -->
    <van-popup v-model:show="showTypePicker" position="bottom">
      <van-picker
        :columns="typeOptions"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>

    <!-- 预算分类选择器 -->
    <van-popup v-model:show="showPlanTypePicker" position="bottom">
      <van-picker
        :columns="planTypeOptions"
        @confirm="onPlanTypeConfirm"
        @cancel="showPlanTypePicker = false"
      />
    </van-popup>



    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import { expertApi } from '@/api/expert'
import { useUserStore } from '@/stores'
import moment from 'moment'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const submitting = ref(false)
const showTypePicker = ref(false)
const showPlanTypePicker = ref(false)
const showDatePicker = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  typeText: '',
  departmentId: '',
  departmentText: '',
  purpose: '',
  planType: '',
  planTypeText: '',
  items: '',
  number: '',
  fundsBudget: '',
  projectDate: '',
  projectDateText: '',
  districtDep: false
})

// 日期相关
const currentDate = ref([moment().format("YYYY"), moment().format("MM"), moment().format("DD")])

// 项目类型选项
const typeOptions = window.$enums.getEnums("ProjectType");

// 预算分类选项
const planTypeOptions = window.$enums.getEnums("ProjectPlanType");



// 返回上一页
const onBack = () => {
  router.back()
}

// 项目类型确认
const onTypeConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0]
  formData.type = option.value
  formData.typeText = option.text
  showTypePicker.value = false
}

// 预算分类确认
const onPlanTypeConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0]
  formData.planType = option.value
  formData.planTypeText = option.text
  showPlanTypePicker.value = false
}



// 日期确认
const onDateConfirm = (value) => {
  currentDate.value = value
  formData.projectDate = formatDate(value)
  showDatePicker.value = false
}

// 格式化日期
const formatDate = (date) => {
  return moment(date).format('YYYY-MM-DD')
}

// 提交表单
const onSubmit = async () => {
  try {
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      name: formData.name,
      type: formData.type,
      departmentId: formData.departmentId,
      purpose: formData.purpose,
      planType: formData.planType,
      items: formData.items,
      number: parseInt(formData.number) || 0,
      fundsBudget: parseInt(formData.fundsBudget) || 0,
      projectDate: formData.projectDate,
      districtDep: formData.districtDep
    }
    
    // 调用API提交
    await expertApi.createProject(submitData)
    
    showSuccessToast('提交成功')
    
    // 延迟返回列表页
    setTimeout(() => {
      router.replace('/user/agency-list')
    }, 1500)
    
  } catch (error) {
    console.error('提交失败:', error)
    showToast(error.message || '提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 初始化用户部门信息
const initUserDepartment = async () => {
  try {
    // 如果没有用户信息，先获取
    if (!userStore.userInfo) {
      await userStore.fetchUserInfo()
    }

    const department = userStore.userDepartment
    if (department) {
      formData.departmentId = department.id || ''
      formData.departmentText = department.name || ''
    } else {
      // 如果没有部门信息，显示提示
      formData.departmentText = '暂无部门信息'
      console.warn('用户部门信息不完整')
    }
  } catch (error) {
    console.error('获取用户部门信息失败:', error)
    formData.departmentText = '获取部门信息失败'
  }
}

// 页面初始化
onMounted(async () => {
  await initUserDepartment()
  console.log('无预算项目前期工作抽取代理机构页面初始化')
})
</script>

<style lang="scss" scoped>
.agency-add-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.form-container {
  background: white;
  margin: 12px;
  border-radius: 8px;
  overflow: hidden;
}

// 必填标签样式
.required-label {
  position: relative;

  &::before {
    content: '*';
    color: #ee0a24;
    font-size: 14px;
    margin-right: 4px;
  }
}

// 覆盖vant表单样式
:deep(.van-form) {
  .van-field {
    padding: 16px;
    border-bottom: 1px solid #f7f8fa;

    &:last-child {
      border-bottom: none;
    }

    .van-field__label {
      color: #323233;
      font-weight: 500;
      font-size: 14px;
      width: 100px;
      flex-shrink: 0;
    }

    .van-field__control {
      font-size: 14px;
      color: #323233;

      &::placeholder {
        color: #c8c9cc;
      }
    }

    .van-field__right-icon {
      color: #969799;
    }
  }

  // 文本域样式
  .van-field--textarea {
    .van-field__control {
      min-height: 60px;
    }
  }
}

// 单位文本样式
.unit-text {
  color: #646566;
  font-size: 14px;
  margin-left: 8px;
}

// 提交按钮区域
.submit-section {
  padding: 24px 16px;
  background: white;
  margin-top: 12px;

  .submit-btn {
    height: 44px;
    background: #07c160;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;

    &:active {
      background: #06ad56;
    }

    &.van-button--loading {
      background: #07c160;
      opacity: 0.7;
    }
  }
}

// 选择器样式覆盖
:deep(.van-popup) {
  .van-picker {
    background: white;

    .van-picker__toolbar {
      padding: 16px;
      border-bottom: 1px solid #f7f8fa;

      .van-picker__confirm {
        color: #07c160;
        font-weight: 500;
      }

      .van-picker__cancel {
        color: #646566;
      }
    }

    .van-picker__columns {
      padding: 16px 0;
    }
  }

  .van-date-picker {
    background: white;

    .van-picker__toolbar {
      padding: 16px;
      border-bottom: 1px solid #f7f8fa;

      .van-picker__confirm {
        color: #07c160;
        font-weight: 500;
      }

      .van-picker__cancel {
        color: #646566;
      }
    }
  }
}

// 导航栏样式覆盖
:deep(.van-nav-bar) {
  background: white;
  border-bottom: 1px solid #f7f8fa;

  .van-nav-bar__title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
  }

  .van-nav-bar__left {
    .van-icon {
      color: #323233;
      font-size: 18px;
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .form-container {
    margin: 8px;
  }

  :deep(.van-field) {
    .van-field__label {
      width: 80px;
      font-size: 13px;
    }

    .van-field__control {
      font-size: 13px;
    }
  }

  .submit-section {
    padding: 20px 12px;

    .submit-btn {
      height: 40px;
      font-size: 15px;
    }
  }
}

// 加载状态
.van-button--loading {
  .van-loading__spinner {
    color: white;
  }
}
</style>
