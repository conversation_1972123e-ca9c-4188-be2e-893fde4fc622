<!-- 抽取记录详情页面 -->
<template>
  <div class="draw-record-detail-page">
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>
    
    <div v-else-if="recordDetail" class="detail-content">
      <!-- 基本信息 -->
      <van-cell-group inset title="基本信息">
        <van-cell title="项目编号" :value="recordDetail.code" />
        <van-cell title="项目名称" :value="recordDetail.name" />
        <van-cell title="项目类型" :value="getProjectTypeText(recordDetail.type)" />
        <van-cell title="申请状态">
          <template #value>
            <van-tag :type="getFlowStatusType(recordDetail.flowStatus)">
              {{ getFlowStatusText(recordDetail.flowStatus) }}
            </van-tag>
          </template>
        </van-cell>
        <van-cell title="项目资金" :value="recordDetail.funds ? `${recordDetail.funds}万元` : '未填写'" />
      </van-cell-group>

      <!-- 申请人信息 -->
      <van-cell-group inset title="申请人信息">
        <van-cell title="申请人" :value="recordDetail.applyUserName || '未知'" />
        <van-cell title="联系电话" :value="recordDetail.applyUserPhone || '未填写'" />
        <van-cell title="申请部门" :value="recordDetail.department || '未知'" />
      </van-cell-group>

      <!-- 经办信息 -->
      <van-cell-group inset title="经办信息">
        <van-cell title="经办人" :value="recordDetail.createUser?.nickname || '未知'" />
        <van-cell title="经办处室" :value="getDepartmentName(recordDetail.createUser)" />
        <van-cell title="经办时间" :value="formatDateTime(recordDetail.createTime)" />
      </van-cell-group>

      <!-- 抽取结果 -->
      <van-cell-group inset title="抽取结果" v-if="recordDetail.expertName || recordDetail.agencyName">
        <van-cell 
          v-if="recordDetail.expertName" 
          title="入选专家" 
          :value="recordDetail.expertName" 
        />
        <van-cell 
          v-if="recordDetail.expertType" 
          title="专家类型" 
          :value="recordDetail.expertType" 
        />
        <van-cell 
          v-if="recordDetail.agencyName" 
          title="入选代理机构" 
          :value="recordDetail.agencyName" 
        />
        <van-cell 
          v-if="recordDetail.drewTime" 
          title="抽取时间" 
          :value="formatDateTime(recordDetail.drewTime)" 
        />
      </van-cell-group>

      <!-- 审批信息 -->
      <van-cell-group inset title="审批信息" v-if="recordDetail.flowUser">
        <van-cell title="审批人" :value="recordDetail.flowUser.nickname || '未知'" />
        <van-cell title="审批人职务" :value="recordDetail.flowUser.post || '未知'" />
        <van-cell title="审批时间" :value="formatDateTime(recordDetail.flowTime)" />
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-section">
        <van-button
          v-if="canEdit(recordDetail)"
          type="primary"
          size="large"
          block
          @click="handleEdit"
        >
          编辑
        </van-button>
        
        <van-button
          v-if="canSubmit(recordDetail)"
          type="success"
          size="large"
          block
          @click="handleSubmit"
          :loading="submitLoading"
        >
          提交申请
        </van-button>
        
        <van-button
          v-if="canRevoke(recordDetail)"
          type="warning"
          size="large"
          block
          @click="handleRevoke"
          :loading="revokeLoading"
        >
          撤回申请
        </van-button>
        
        <van-button
          v-if="canExecute(recordDetail)"
          type="primary"
          size="large"
          block
          @click="handleExecute"
          :loading="executeLoading"
        >
          执行抽取
        </van-button>
        
        <van-button
          v-if="canDelete(recordDetail)"
          type="danger"
          size="large"
          block
          @click="handleDelete"
          :loading="deleteLoading"
        >
          删除
        </van-button>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-else
      description="抽取记录不存在"
      image="error"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { drawApi } from '@/api/draw'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const revokeLoading = ref(false)
const executeLoading = ref(false)
const deleteLoading = ref(false)
const recordDetail = ref(null)

// 方法
const fetchRecordDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    const response = await drawApi.getDrawRecordDetail(id)
    recordDetail.value = response
  } catch (error) {
    console.error('获取抽取记录详情失败:', error)
    showToast('获取详情失败')
    
    // 使用模拟数据
    recordDetail.value = {
      id: route.params.id,
      entityId: '1',
      applyStatus: 'APPROVED',
      name: '2024年市级供销合作社培育壮大工程专项资金',
      code: '88839003003',
      type: 'REVIEW',
      initFlag: true,
      flowStatus: 'APPROVAL',
      flowUser: {
        id: '1',
        nickname: '张三',
        post: '科员'
      },
      flowTime: '2025-12-12 12:22:22',
      history: false,
      drawStatus: null,
      drewTime: null,
      department: '成都市农业农村局',
      applyUserName: '李四',
      applyUserPhone: '138****5678',
      expertName: '张三',
      expertType: '农业专家',
      agencyName: null,
      funds: '100',
      createUser: {
        id: '1',
        nickname: '张三',
        post: '科员',
        groups: [{
          name: '成都市农业农村局'
        }]
      },
      createTime: '2025-12-12 12:22:22'
    }
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push(`/gov/draw-records/edit/${route.params.id}`)
}

const handleSubmit = async () => {
  try {
    await showConfirmDialog({
      title: '确认提交',
      message: '确定要提交该抽取申请吗？'
    })
    
    submitLoading.value = true
    await drawApi.submitDrawRecord(route.params.id)
    
    showSuccessToast('提交成功')
    fetchRecordDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      showToast('提交失败，请重试')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleRevoke = async () => {
  try {
    await showConfirmDialog({
      title: '确认撤回',
      message: '确定要撤回该抽取申请吗？'
    })
    
    revokeLoading.value = true
    await drawApi.revokeDrawRecord(route.params.id)
    
    showSuccessToast('撤回成功')
    fetchRecordDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤回失败:', error)
      showToast('撤回失败，请重试')
    }
  } finally {
    revokeLoading.value = false
  }
}

const handleExecute = async () => {
  try {
    await showConfirmDialog({
      title: '确认执行',
      message: '确定要执行抽取吗？'
    })
    
    executeLoading.value = true
    await drawApi.executeDrawRecord(route.params.id)
    
    showSuccessToast('抽取成功')
    fetchRecordDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行抽取失败:', error)
      showToast('执行失败，请重试')
    }
  } finally {
    executeLoading.value = false
  }
}

const handleDelete = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '删除后无法恢复，确定要删除该抽取记录吗？'
    })
    
    deleteLoading.value = true
    await drawApi.deleteDrawRecord(route.params.id)
    
    showSuccessToast('删除成功')
    router.back()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      showToast('删除失败，请重试')
    }
  } finally {
    deleteLoading.value = false
  }
}

// 权限判断方法
const canEdit = (record) => {
  return record.flowStatus === 'UN_SUBMIT'
}

const canSubmit = (record) => {
  return record.flowStatus === 'UN_SUBMIT'
}

const canRevoke = (record) => {
  return record.flowStatus === 'APPROVAL'
}

const canExecute = (record) => {
  return record.flowStatus === 'FINISH' && !record.expertName && !record.agencyName
}

const canDelete = (record) => {
  return record.flowStatus === 'UN_SUBMIT' || record.flowStatus === 'REJECT'
}

const getProjectTypeText = (type) => {
  const typeMap = {
    'REVIEW': '抽取专家',
    'PURCHASE': '抽取代理机构',
    'EVADE': '专家规避',
    'REVOKE': '代理机构撤回'
  }
  return typeMap[type] || type
}

const getFlowStatusText = (status) => {
  const statusMap = {
    'UN_SUBMIT': '未提交',
    'APPROVAL': '审批中',
    'REJECT': '已驳回',
    'REVOKE': '已撤回',
    'FINISH': '审批通过',
    'REJECT_FINISH': '撤回拒绝'
  }
  return statusMap[status] || status
}

const getFlowStatusType = (status) => {
  const typeMap = {
    'UN_SUBMIT': 'default',
    'APPROVAL': 'warning',
    'REJECT': 'danger',
    'REVOKE': 'default',
    'FINISH': 'success',
    'REJECT_FINISH': 'danger'
  }
  return typeMap[status] || 'default'
}

const getDepartmentName = (user) => {
  return user?.groups?.[0]?.name || '未知部门'
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  // 如果已经是格式化的字符串，直接返回
  if (dateString.includes('-') && dateString.includes(':')) {
    return dateString
  }
  // 否则格式化日期
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

// 生命周期
onMounted(() => {
  fetchRecordDetail()
})

onActivated(() => {
  fetchRecordDetail()
})
</script>

<style lang="scss" scoped>
.draw-record-detail-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  .van-cell-group {
    margin-bottom: 16px;
  }
}

.action-section {
  margin-top: 32px;
  
  .van-button {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

<route lang="json5">
{
  name: 'DrawRecordDetail',
  meta: {
    title: '抽取记录详情'
  }
}
</route>
