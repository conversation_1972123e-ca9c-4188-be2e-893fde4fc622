/**
 * v-loading 指令
 * 用于在指定的 DOM 容器上显示局部 loading 效果
 * 
 * 使用方式：
 * <div v-loading="isLoading">内容</div>
 * <div v-loading="{ loading: isLoading, text: '加载中...', background: 'rgba(0,0,0,0.8)' }">内容</div>
 */

// 创建 loading 元素
const createLoadingElement = (options = {}) => {
  const {
    text = '加载中...',
    background = 'rgba(255, 255, 255, 0.7)',
    spinnerColor = 'var(--van-primary-color)',
    textColor = '#666',
    zIndex = 2000
  } = options

  // 创建遮罩层
  const mask = document.createElement('div')
  mask.className = 'v-loading-mask'
  mask.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${background};
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: ${zIndex};
    transition: opacity 0.3s ease;
  `

  // 创建加载动画容器
  const spinner = document.createElement('div')
  spinner.className = 'v-loading-spinner'
  spinner.style.cssText = `
    width: 32px;
    height: 32px;
    border: 3px solid transparent;
    border-top: 3px solid ${spinnerColor};
    border-radius: 50%;
    animation: v-loading-spin 1s linear infinite;
    margin-bottom: 12px;
  `

  // 创建加载文本
  const textElement = document.createElement('div')
  textElement.className = 'v-loading-text'
  textElement.textContent = text
  textElement.style.cssText = `
    font-size: 14px;
    color: ${textColor};
    line-height: 1.4;
    text-align: center;
  `

  // 组装元素
  mask.appendChild(spinner)
  // mask.appendChild(textElement)

  return mask
}

// 添加 CSS 动画
const addLoadingStyles = () => {
  if (document.getElementById('v-loading-styles')) return

  const style = document.createElement('style')
  style.id = 'v-loading-styles'
  style.textContent = `
    @keyframes v-loading-spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .v-loading-mask {
      pointer-events: auto;
    }
    
    .v-loading-mask * {
      pointer-events: none;
    }
  `
  document.head.appendChild(style)
}

// 显示 loading
const showLoading = (el, options) => {
  // 确保容器是相对定位
  const position = window.getComputedStyle(el).position
  if (position === 'static') {
    el.style.position = 'relative'
  }

  // 移除已存在的 loading
  hideLoading(el)

  // 创建新的 loading 元素
  const loadingEl = createLoadingElement(options)
  loadingEl.setAttribute('data-v-loading', 'true')
  
  // 添加到容器中
  el.appendChild(loadingEl)
  
  // 存储 loading 元素引用
  el._vLoadingElement = loadingEl
}

// 隐藏 loading
const hideLoading = (el) => {
  const loadingEl = el.querySelector('[data-v-loading="true"]')
  if (loadingEl) {
    loadingEl.style.opacity = '0'
    setTimeout(() => {
      if (loadingEl.parentNode) {
        loadingEl.parentNode.removeChild(loadingEl)
      }
    }, 300)
  }
  el._vLoadingElement = null
}

// 解析指令值
const parseDirectiveValue = (value) => {
  if (typeof value === 'boolean') {
    return { loading: value }
  }
  
  if (typeof value === 'object' && value !== null) {
    return value
  }
  
  return { loading: !!value }
}

// 指令定义
const loadingDirective = {
  mounted(el, binding) {
    // 添加样式
    addLoadingStyles()
    
    const options = parseDirectiveValue(binding.value)
    
    if (options.loading) {
      showLoading(el, options)
    }
  },
  
  updated(el, binding) {
    const options = parseDirectiveValue(binding.value)
    const oldOptions = parseDirectiveValue(binding.oldValue)
    
    // 如果 loading 状态发生变化
    if (options.loading !== oldOptions.loading) {
      if (options.loading) {
        showLoading(el, options)
      } else {
        hideLoading(el)
      }
    } else if (options.loading) {
      // 如果 loading 状态没变但其他选项变了，重新创建
      const optionsChanged = JSON.stringify(options) !== JSON.stringify(oldOptions)
      if (optionsChanged) {
        hideLoading(el)
        showLoading(el, options)
      }
    }
  },
  
  unmounted(el) {
    hideLoading(el)
  }
}

export default loadingDirective
