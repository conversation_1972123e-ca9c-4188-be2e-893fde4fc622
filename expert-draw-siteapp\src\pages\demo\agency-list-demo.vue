<!-- 代理机构列表示例 -->
<template>
  <div class="agency-list-demo">
    <van-nav-bar title="代理机构列表示例" left-arrow @click-left="$router.back()" />
    
    <FuniList
      ref="agencyListRef"
      :tabs="tabsConfig"
      item-key="id"
      empty-text="暂无代理机构数据"
      @item-click="handleAgencyClick"
    >
      <template #item="{ item: agency, index }">
        <div class="agency-card">
          <div class="agency-header">
            <div class="agency-name">{{ agency.name }}</div>
            <div class="agency-status" :class="{ active: agency.enabled }">
              {{ agency.enabled ? '启用' : '禁用' }}
            </div>
          </div>
          
          <div class="agency-info">
            <div class="info-row">
              <span class="label">负责人</span>
              <span class="value">{{ agency.leader }}</span>
            </div>
            <div class="info-row">
              <span class="label">电话</span>
              <span class="value">{{ agency.leaderPhone }}</span>
            </div>
            <div class="info-row">
              <span class="label">经办人</span>
              <span class="value">{{ agency.contact }} {{ agency.phone }}</span>
            </div>
            <div class="info-row">
              <span class="label">服务范围</span>
              <span class="value">{{ getServiceRangeText(agency.serviceRange) }}</span>
            </div>
          </div>
        </div>
      </template>

      <template #footer>
        <!-- 浮动添加按钮 -->
        <van-floating-bubble
          axis="xy"
          icon="plus"
          @click="handleAddAgency"
        />
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import FuniList from '@/components/FuniList.vue'

const router = useRouter()
const agencyListRef = ref()

// 服务范围映射
const serviceRangeMap = {
  'ALL': '全部',
  'DIRECTLY': '局机关和直属单位',
  'DISTRICT': '区县'
}

// Tabs 配置
const tabsConfig = [
  {
    key: 'agencies',
    title: '代理机构',
    loadFunction: loadAgencyData,
    searchPlaceholder: '搜索机构名称、联系人、电话',
    keyword: 'keyword',
    filterConfig: [
      {
        type: 'select',
        label: '状态',
        prop: 'enabled',
        options: [
          { text: '全部', value: '' },
          { text: '启用', value: true },
          { text: '禁用', value: false }
        ],
        props: {
          placeholder: '请选择状态'
        }
      },
      {
        type: 'select',
        label: '服务范围',
        prop: 'serviceRange',
        options: [
          { text: '全部', value: '' },
          { text: '全部', value: 'ALL' },
          { text: '局机关和直属单位', value: 'DIRECTLY' },
          { text: '区县', value: 'DISTRICT' }
        ],
        props: {
          placeholder: '请选择服务范围'
        }
      }
    ]
  }
]

// 模拟数据生成
const generateMockAgencies = (page = 1, pageSize = 20) => {
  const agencies = []
  const names = [
    '四川鲁通天和招标代理有限公司',
    '成都市政府采购代理有限公司',
    '四川省招标投标代理有限公司',
    '绵阳市采购代理服务中心',
    '德阳市政府采购中心'
  ]
  
  const serviceRanges = ['ALL', 'DIRECTLY', 'DISTRICT']
  
  for (let i = 0; i < pageSize; i++) {
    const index = (page - 1) * pageSize + i
    agencies.push({
      id: `agency_${index + 1}`,
      name: `${names[index % names.length]} ${Math.floor(index / names.length) + 1}`,
      contact: `经办人${index + 1}`,
      phone: `187****${String(3000 + index).padStart(4, '0')}`,
      contactb: index % 3 === 0 ? `经办人${index + 1}B` : '',
      phoneb: index % 3 === 0 ? `187****${String(4000 + index).padStart(4, '0')}` : '',
      leader: `负责人${index + 1}`,
      leaderPhone: `187****${String(5000 + index).padStart(4, '0')}`,
      enabled: index % 4 !== 0, // 大部分启用，少部分禁用
      serviceRange: serviceRanges[index % serviceRanges.length],
      createTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
    })
  }
  
  return agencies
}

// 数据加载函数
const loadAgencyData = async (params) => {
  try {
    console.log('加载代理机构数据:', params)
    
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    
    let mockData = generateMockAgencies(params.page, params.pageSize)
    
    // 模拟搜索过滤
    if (params.keyword && params.keyword.trim()) {
      const keyword = params.keyword.trim().toLowerCase()
      mockData = mockData.filter(agency => 
        agency.name.toLowerCase().includes(keyword) ||
        agency.contact.toLowerCase().includes(keyword) ||
        agency.leader.toLowerCase().includes(keyword) ||
        agency.phone.includes(keyword) ||
        agency.leaderPhone.includes(keyword)
      )
    }
    
    // 模拟状态过滤
    if (params.enabled !== undefined && params.enabled !== '') {
      mockData = mockData.filter(agency => agency.enabled === params.enabled)
    }
    
    // 模拟服务范围过滤
    if (params.serviceRange && params.serviceRange !== '') {
      mockData = mockData.filter(agency => agency.serviceRange === params.serviceRange)
    }
    
    return {
      data: mockData,
      total: 150 // 模拟总数
    }
    
  } catch (error) {
    console.error('获取代理机构列表失败:', error)
    throw error
  }
}

// 事件处理方法
const handleAgencyClick = (agency, index) => {
  console.log('点击代理机构:', agency, index)
  showToast(`点击了: ${agency.name}`)
}

const handleAddAgency = () => {
  showToast('添加代理机构功能')
}

const getServiceRangeText = (range) => {
  return serviceRangeMap[range] || range
}
</script>

<style lang="scss" scoped>
.agency-list-demo {
  height: 100vh;
  background-color: var(--van-background);
}

.agency-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.agency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.agency-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  flex: 1;
  margin-right: 12px;
}

.agency-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: var(--van-gray-3);
  color: var(--van-text-color-2);

  &.active {
    background-color: var(--van-success-color);
    color: white;
  }
}

.agency-info {
  .info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .label {
    color: var(--van-text-color-2);
    width: 80px;
    flex-shrink: 0;
  }

  .value {
    color: var(--van-text-color);
    flex: 1;
  }
}
</style>

<route lang="json5">
{
  name: 'AgencyListDemo',
  meta: {
    title: '代理机构列表示例'
  }
}
</route>
