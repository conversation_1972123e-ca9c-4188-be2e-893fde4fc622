<!-- 新增专家种类页面 -->
<template>
  <div class="category-add-page">
    <van-form @submit="handleSubmit" ref="formRef">
      <van-cell-group inset>
        <van-field
          v-model="formData.name"
          name="name"
          label="种类名称"
          placeholder="请输入专家种类名称"
          :rules="[{ required: true, message: '请输入专家种类名称' }]"
        />
        
        <van-field
          v-model="formData.description"
          name="description"
          label="种类描述"
          type="textarea"
          placeholder="请输入专家种类描述"
          rows="3"
          autosize
          :rules="[{ required: true, message: '请输入专家种类描述' }]"
        />
        
        <van-field
          v-model="formData.plNumber"
          name="plNumber"
          label="初始人数"
          type="number"
          placeholder="请输入初始专家人数"
          :rules="[
            { required: true, message: '请输入初始专家人数' },
            { pattern: /^\d+$/, message: '请输入有效的数字' }
          ]"
        />
        
        <van-field name="enabled" label="状态">
          <template #input>
            <van-switch v-model="formData.enabled" />
          </template>
        </van-field>
      </van-cell-group>
      
      <div class="submit-section">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="submitting"
        >
          {{ submitting ? '提交中...' : '提交' }}
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import { expertApi } from '@/api/expert'

const router = useRouter()

// 响应式数据
const formRef = ref()
const submitting = ref(false)

const formData = reactive({
  name: '',
  description: '',
  plNumber: '0',
  enabled: true
})

// 方法
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      plNumber: formData.plNumber.toString(),
      enabled: formData.enabled.toString()
    }
    
    // 这里应该调用创建专家种类的API
    // await expertApi.createExpertCategory(submitData)
    
    showSuccessToast('专家种类添加成功')
    
    // 返回列表页面
    router.back()
    
  } catch (error) {
    console.error('添加专家种类失败:', error)
    showToast('添加失败，请重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.category-add-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 100px;
  flex-shrink: 0;
}

:deep(.van-field__control) {
  text-align: right;
}
</style>

<route lang="json5">
{
  name: 'ExpertCategoryAdd',
  meta: {
    title: '新增专家种类'
  }
}
</route>
