<!-- 专家详情页面 -->
<template>
  <div class="expert-detail-page" v-loading="loading">
    <!-- 专家详情内容 -->
    <div v-if="expertInfo" class="expert-detail-content">
      <!-- 专家信息列表 -->
      <div class="detail-list">
        <!-- 类型 -->
        <div class="detail-item">
          <span class="label">类型</span>
          <span class="value">{{ expertInfo.type?.name }}</span>
        </div>

        <!-- 姓名 -->
        <div class="detail-item">
          <span class="label">姓名</span>
          <span class="value">{{ expertInfo.name }}</span>
        </div>

        <!-- 性别 -->
        <div class="detail-item">
          <span class="label">性别</span>
          <span class="value">{{ getGenderText(expertInfo.gender) }}</span>
        </div>

        <!-- 出生日期 -->
        <div class="detail-item">
          <span class="label">出生日期</span>
          <span class="value">{{ expertInfo.birthday }}</span>
        </div>

        <!-- 身份证号码 -->
        <div class="detail-item">
          <span class="label">身份证号码</span>
          <span class="value">{{ expertInfo.idNum }}</span>
        </div>

        <!-- 单位职务 -->
        <div class="detail-item">
          <span class="label">单位职务</span>
          <span class="value">{{ expertInfo.post }}</span>
        </div>

        <!-- 所在领域 -->
        <div class="detail-item">
          <span class="label">所在领域</span>
          <span class="value">{{ expertInfo.field }}</span>
        </div>

        <!-- 专业级别(技术职称) -->
        <div class="detail-item">
          <span class="label">专业级别(技术职称)</span>
          <span class="value">{{ expertInfo.level }}</span>
        </div>

        <!-- 手机号码 -->
        <div class="detail-item">
          <span class="label">手机号码</span>
          <span class="value">{{ expertInfo.phone }}</span>
        </div>

        <!-- 座机号码 -->
        <div class="detail-item">
          <span class="label">座机号码</span>
          <span class="value">{{ expertInfo.landline }}</span>
        </div>

        <!-- 服务范围 -->
        <div class="detail-item">
          <span class="label">服务范围</span>
          <span class="value">{{ getServiceRangeText(expertInfo.serviceRange) }}</span>
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="action-section">
        <van-button 
          type="default" 
          size="large" 
          class="back-btn"
          @click="onBack"
        >
          返回
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import { expertApi } from '@/api/expert'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const expertInfo = ref(null)

// 获取专家ID
const expertId = route.query.id


// 获取性别文本
const getGenderText = (gender) => {
  return window.$enums.getEnumText('Gender',gender)
}

// 获取服务范围文本
const getServiceRangeText = (serviceRange) => {
  return window.$enums.getEnumText('ServiceRange',serviceRange)
}

// 加载专家详情
const loadExpertDetail = async () => {
  try {
    loading.value = true
    // 调用API获取专家详情
    const response = await expertApi.getExpertDetail(expertId)
    expertInfo.value = response.data
    
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadExpertDetail()
})
</script>

<style lang="scss" scoped>
.expert-detail-page {
  min-height: calc(100vh - 46px);
  background: #f5f5f5;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.expert-detail-content {
  padding: 16px;
}

.detail-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .label {
    width: 120px;
    font-size: 14px;
    color: #646566;
    flex-shrink: 0;
  }
  
  .value {
    flex: 1;
    font-size: 14px;
    color: #323233;
    text-align: right;
    word-break: break-all;
    :empty::after{
        content:"--"
    }
  }
}

.action-section {
  padding: 0 16px 32px;
  
  .back-btn {
    width: 100%;
    height: 44px;
    background: white;
    color: #323233;
    border: 1px solid #ebedf0;
    border-radius: 6px;
    font-size: 16px;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  padding: 32px;
  
  .van-button {
    margin-top: 16px;
  }
}

// 覆盖vant样式
:deep(.van-nav-bar) {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  
  .van-nav-bar__title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
  }
  
  .van-icon {
    color: #323233;
  }
}

:deep(.van-empty) {
  padding: 32px 0;
}
</style>
<route lang="json5">
{
  name: "expertDetail",
  meta: {
    title: "专家详情",
  },
}
</route>