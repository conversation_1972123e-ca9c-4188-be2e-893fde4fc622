<!-- 专家详情页面 -->
<template>
  <div class="expert-detail-page" v-loading="loading">
    <!-- 专家详情内容 -->
      <van-cell-group>
            <van-cell title="类型:" :value="expertInfo.type?.name || '--'" />
            <van-cell title="姓名:" :value="expertInfo.name || '--'" />
            <van-cell title="性别:" :value="getGenderText(expertInfo.gender) || '--'" />
            <van-cell title="出生日期:" :value="expertInfo.birthday || '--'" />
            <van-cell title="身份证号码:" :value="expertInfo.idNum || '--'" />
            <van-cell title="单位职务:" :value="expertInfo.post || '--'" />
            <van-cell title="所在领域:" :value="expertInfo.field || '--'" />
            <van-cell title="专业级别(技术职称):" :value="expertInfo.level || '--'" />
            <van-cell title="手机号码:" :value="expertInfo.phone || '--'" />
            <van-cell title="座机号码:" :value="expertInfo.landline || '--'" />
            <van-cell title="服务范围:" :value="getServiceRangeText(expertInfo.serviceRange) || '--'" />
      </van-cell-group>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import { expertApi } from '@/api/expert'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const expertInfo = ref({})

// 获取专家ID
const expertId = route.query.id


// 获取性别文本
const getGenderText = (gender) => {
  return window.$enums.getEnumText('Gender',gender)
}

// 获取服务范围文本
const getServiceRangeText = (serviceRange) => {
  return window.$enums.getEnumText('ServiceRange',serviceRange)
}

// 加载专家详情
const loadExpertDetail = async () => {
  try {
    loading.value = true
    // 调用API获取专家详情
    const response = await expertApi.getExpertDetail(expertId)
    expertInfo.value = response.data
    
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadExpertDetail()
})
</script>

<style lang="scss" scoped>
.expert-detail-page {
  min-height: calc(100vh - 46px);
  padding-top: 12px;
  ::v-deep(){
    .van-cell__title{
      color:var(van-cell-value-color);
    }
    .van-cell__value{
      color:#000;
    }
  }
}

</style>
<route lang="json5">
{
  name: "expertDetail",
  meta: {
    title: "专家详情",
  },
}
</route>