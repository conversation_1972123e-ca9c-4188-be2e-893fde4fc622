<!-- 采购管理页面 -->
<template>
  <div class="records-page">
    <FuniList
      ref="recordsListRef"
      :tabs="tabsConfig"
      :search-placeholder="searchPlaceholder"
      item-key="id"
      @item-click="handleItemClick"
      @tab-change="handleTabChange"
    >
      <template #item="{ item: record, index }">
        <!-- 项目标题 -->
        <div class="record-title">
          <div class="item-title">{{ record.name || "--" }}</div>
        </div>

        <!-- 项目信息 -->
        <div class="project-details">
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">项目分类</span>
              <span class="value">{{ getProjectType(record.type) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">承办处室</span>
              <span class="value">{{ record.department.name }}</span>
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-item">
              <span class="label">采购品目</span>
              <span class="value">{{ record.items }}</span>
            </div>
            <div class="detail-item">
              <span class="label">数量</span>
              <span class="value">{{ record.number }}</span>
            </div>
          </div>

          <div class="detail-row">
            <div class="detail-item">
              <span class="label">采购内容（用途）</span>
              <span class="value">{{ record.purpose }}</span>
            </div>
            <div class="detail-item">
              <span class="label">预算分类</span>
              <span class="value">{{ getProjectPlanType(record.purpose) }}</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="label">经费预算（万元）</span>
              <span class="value">{{ record.planFundsBudget }}</span>
            </div>
            <div class="detail-item">
              <span class="label">已发起申请</span>
              <span class="value">{{ record.drew ? '是' : '否' }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="record-actions">
          <van-button size="small" block @click="toDel(record)">
            删除
          </van-button>
          <van-button size="small" block  type="success" v-if="!record.drew" @click="toEdit(record)">
            编辑
          </van-button>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";
import moment from "moment";
import { getList } from "@/api/procurement";
import FuniList from "@/components/FuniList.vue";

const router = useRouter();
const recordsListRef = ref();

// 搜索placeholder
const searchPlaceholder = ref("模糊检索项目名称");

// Tabs 配置
const tabsConfig = computed(() => [
  {
    key: "NORMAL",
    title: "正常",
    loadFunction: loadNormalRecords,
    extraParams: {
      projectStatus: "NORMAL",
    },
    keyword: "name",
    filterConfig: getFilterConfig(),
  },
  {
    key: "CANCEL",
    title: "已作废",
    loadFunction: loadCancelRecords,
    extraParams: {
      projectStatus: "CANCEL",
    },
    keyword: "name",
    filterConfig: getFilterConfig(),
  },
]);

// 筛选配置
const getFilterConfig = () => [
  {
    type: "input",
    label: "承办处室",
    prop: "departmentName",
    props: {
      placeholder: "请输入",
      clearable: true,
    },
  },
  {
    type: "datetime",
    label: "经办开始时间",
    prop: "flowTimeStart",
    props: {},
  },
  {
    type: "datetime",
    label: "经办结束时间",
    prop: "flowTimeEnd",
    props: {},
  },
  {
    type: "select",
    label: "项目类型",
    prop: "applyType",
    options: window.$enums.getEnums("ApplyType"),
  },
  {
    type: "select",
    label: "项目状态",
    prop: "flowStatus",
    options: window.$enums.getEnums("FlowStatus"),
  },
];

// 数据加载函数
async function loadNormalRecords(params) {
  const response = await getList(params);
  return {
    data: response.data || [],
    total: response.total || 0,
  };
}

async function loadCancelRecords(params) {
  const response = await getList(params);
  return {
    data: response.data || [],
    total: response.total || 0,
  };
}

// 事件处理方法
const handleItemClick = (record, index) => {
  console.log("点击记录:", record, index);
  toDetail(record);
};

const handleTabChange = (tabKey, tab) => {
  console.log("Tab 切换:", tabKey, tab);
};

const getProjectType = (type) => {
  return window.$enums.getEnumText("ProjectType", type);
};

const getProjectPlanType = (type) => {
  return window.$enums.getEnumText("ProjectPlanType", type);
};

const toDetail = (record) => {
  router.push(`/user/expert-apply/detail/${record.entityId}`);
};

function toEdit(record){}
function toDel(record){}
</script>

<style lang="scss" scoped>
.records-page {
  height: calc(100vh - 46px);
  .project-details {
    .detail-row {
      display: flex;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-item {
        flex: 1;
        display: flex;
        flex-direction: column;

        .label {
          font-size: 12px;
          color: #606266;
          margin-bottom: 4px;
        }

        .value {
          font-size: 14px;
          color: #000;
          font-weight: 500;
          &:empty::after {
            content: "--";
          }
        }
      }
    }
  }
  .record-actions{
    padding-top: 12px;
    display: flex;
    gap:12px;
  }
}
</style>

<route lang="json5">
{
  name: "procurementList",
  meta: {
    title: "采购管理",
  },
}
</route>
