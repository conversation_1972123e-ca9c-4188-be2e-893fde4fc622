<!-- 采购管理列表页面 -->
<template>
  <div class="procurement-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索专家姓名"
        @search="handleSearch"
        @clear="handleClear"
        show-action
      >
        <template #action>
          <div @click="handleSearch" class="search-action">筛选</div>
        </template>
      </van-search>
    </div>

    <!-- 采购项目列表 -->
    <div class="procurement-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="project in projectList"
            :key="project.id"
            class="project-card"
          >
            <!-- 项目标题 -->
            <div class="project-header">
              <div class="project-title">【{{ project.category }}】{{ project.name }}</div>
              <div class="project-status" :class="getStatusClass(project.status)">
                {{ getStatusText(project.status) }}
              </div>
            </div>

            <!-- 项目详细信息 -->
            <div class="project-details">
              <div class="detail-row">
                <div class="detail-item">
                  <span class="detail-label">项目分类</span>
                  <span class="detail-value">{{ project.category }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">经办人</span>
                  <span class="detail-value">{{ project.handler }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="detail-label">宜置单位</span>
                  <span class="detail-value">{{ project.procuringEntity }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">经办时间</span>
                  <span class="detail-value">{{ formatDateTime(project.handleTime) }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="detail-label">成都市农业农村局</span>
                  <span class="detail-value">{{ formatDateTime(project.createTime) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">采购日期</span>
                  <span class="detail-value">{{ formatDate(project.procurementDate) }}</span>
                </div>
              </div>
              
              <div class="detail-row">
                <div class="detail-item">
                  <span class="detail-label">采购日期</span>
                  <span class="detail-value">{{ formatDate(project.procurementDate) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">项目状态</span>
                  <span class="detail-value">{{ getStatusText(project.status) }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
              <van-button 
                type="default" 
                size="small" 
                @click="handleDetail(project)"
              >
                详情
              </van-button>
              
              <van-button 
                type="warning" 
                size="small" 
                @click="handleDelete(project)"
              >
                删除
              </van-button>
              
              <van-button 
                type="success" 
                size="small" 
                @click="handleEdit(project)"
              >
                编辑
              </van-button>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && projectList.length === 0"
      description="暂无采购项目"
      image="search"
    />

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAddProject"
      class="add-button"
    >
      <div class="add-text">新增采购项目申请表</div>
    </van-floating-bubble>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { procurementApi } from '@/api/procurement'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const projectList = ref([])
const currentPage = ref(1)
const pageSize = ref(20)

// 方法
const fetchProjectList = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1
      finished.value = false
    }
    
    loading.value = true
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value
    }
    
    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }
    
    const response = await procurementApi.getProjectList(params)
    
    if (isRefresh) {
      projectList.value = response.list || response || []
    } else {
      projectList.value.push(...(response.list || response || []))
    }
    
    // 判断是否还有更多数据
    if (!response.list || response.list.length < pageSize.value) {
      finished.value = true
    }
    
  } catch (error) {
    console.error('获取采购项目列表失败:', error)
    showToast('获取数据失败')
    
    // 使用模拟数据
    if (isRefresh || projectList.value.length === 0) {
      projectList.value = [
        {
          id: '1',
          name: '抽取招标代理机构和评审专家',
          category: '测试无审请项目',
          handler: '张三',
          procuringEntity: '张三',
          handleTime: '2025-12-12 12:22:22',
          createTime: '2025-12-12 12:22:22',
          procurementDate: '2025-12-12',
          status: 'DRAFT'
        },
        {
          id: '2',
          name: '抽取招标代理机构和评审专家',
          category: '测试无审请项目',
          handler: '张三',
          procuringEntity: '张三',
          handleTime: '2025-12-12 12:22:22',
          createTime: '2025-12-12 12:22:22',
          procurementDate: '2025-12-12',
          status: 'PENDING'
        }
      ]
      finished.value = true
    }
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onLoad = () => {
  if (!finished.value) {
    currentPage.value++
    fetchProjectList()
  }
}

const onRefresh = () => {
  fetchProjectList(true)
}

const handleSearch = () => {
  fetchProjectList(true)
}

const handleClear = () => {
  searchKeyword.value = ''
  fetchProjectList(true)
}

const handleDetail = (project) => {
  router.push(`/gov/procurement/detail/${project.id}`)
}

const handleEdit = (project) => {
  router.push(`/gov/procurement/edit/${project.id}`)
}

const handleDelete = async (project) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '删除后无法恢复，确定要删除该采购项目吗？'
    })
    
    await procurementApi.deleteProject(project.id)
    showSuccessToast('删除成功')
    fetchProjectList(true)
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      showToast('删除失败，请重试')
    }
  }
}

const handleAddProject = () => {
  router.push('/gov/procurement/add')
}

const getStatusText = (status) => {
  const statusMap = {
    'DRAFT': '草稿',
    'PENDING': '待审批',
    'APPROVED': '已通过',
    'REJECTED': '已驳回',
    'COMPLETED': '已完成'
  }
  return statusMap[status] || status
}

const getStatusClass = (status) => {
  const classMap = {
    'DRAFT': 'status-draft',
    'PENDING': 'status-pending',
    'APPROVED': 'status-approved',
    'REJECTED': 'status-rejected',
    'COMPLETED': 'status-completed'
  }
  return classMap[status] || ''
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  // 如果已经是格式化的字符串，直接返回
  if (dateString.includes('-') && dateString.includes(':')) {
    return dateString
  }
  // 否则格式化日期
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  // 如果已经是格式化的字符串，直接返回
  if (dateString.includes('-')) {
    return dateString
  }
  // 否则格式化日期
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN').replace(/\//g, '-')
}

// 生命周期
onMounted(() => {
  fetchProjectList(true)
})

onActivated(() => {
  fetchProjectList(true)
})
</script>

<style lang="scss" scoped>
.procurement-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.search-section {
  background: white;
  padding: 8px 0;
  border-bottom: 1px solid var(--van-border-color);
  
  .search-action {
    color: var(--van-text-color);
    font-size: 14px;
  }
}

.procurement-list {
  padding: 8px 16px;
}

.project-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.project-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.project-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  flex-shrink: 0;
  
  &.status-draft {
    background-color: var(--van-gray-1);
    color: var(--van-text-color-2);
  }
  
  &.status-pending {
    background-color: #FFF3E0;
    color: #FF9800;
  }
  
  &.status-approved {
    background-color: #E8F5E8;
    color: #4CAF50;
  }
  
  &.status-rejected {
    background-color: #FFEBEE;
    color: #F44336;
  }
  
  &.status-completed {
    background-color: #E3F2FD;
    color: #2196F3;
  }
}

.project-details {
  margin-bottom: 12px;
  
  .detail-row {
    display: flex;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-item {
    flex: 1;
    
    &:first-child {
      margin-right: 16px;
    }
  }

  .detail-label {
    font-size: 12px;
    color: var(--van-text-color-2);
    margin-bottom: 2px;
    display: block;
  }

  .detail-value {
    font-size: 14px;
    color: var(--van-text-color);
  }
}

.action-section {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid var(--van-border-color);
}

.add-button {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  
  .add-text {
    color: white;
    font-size: 12px;
    font-weight: 500;
  }
}
</style>

<route lang="json5">
{
  name: 'ProcurementManagement',
  meta: {
    title: '采购管理'
  }
}
</route>
