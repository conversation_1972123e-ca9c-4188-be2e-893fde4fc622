<!-- 编辑专家种类页面 -->
<template>
  <div class="category-edit-page">
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>
    
    <van-form v-else @submit="handleSubmit" ref="formRef">
      <van-cell-group inset>
        <van-field
          v-model="formData.name"
          name="name"
          label="种类名称"
          placeholder="请输入专家种类名称"
          :rules="[{ required: true, message: '请输入专家种类名称' }]"
        />
        
        <van-field
          v-model="formData.description"
          name="description"
          label="种类描述"
          type="textarea"
          placeholder="请输入专家种类描述"
          rows="3"
          autosize
          :rules="[{ required: true, message: '请输入专家种类描述' }]"
        />
        
        <van-field
          v-model="formData.plNumber"
          name="plNumber"
          label="专家人数"
          type="number"
          placeholder="请输入专家人数"
          :rules="[
            { required: true, message: '请输入专家人数' },
            { pattern: /^\d+$/, message: '请输入有效的数字' }
          ]"
        />
        
        <van-field name="enabled" label="状态">
          <template #input>
            <van-switch v-model="formData.enabled" />
          </template>
        </van-field>
      </van-cell-group>
      
      <div class="submit-section">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="submitting"
        >
          {{ submitting ? '保存中...' : '保存' }}
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import { expertApi } from '@/api/expert'

const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

const formData = reactive({
  name: '',
  description: '',
  plNumber: '0',
  enabled: true
})

// 方法
const fetchCategoryDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    
    // 这里应该调用获取专家种类详情的API
    // const response = await expertApi.getExpertCategoryDetail(id)
    
    // 使用模拟数据
    const response = {
      id: id,
      name: '财务审计类',
      description: '具备财务管理、会计、审计等专业背景的专家',
      plNumber: '59',
      enabled: 'true'
    }
    
    // 填充表单数据
    Object.assign(formData, {
      name: response.name || '',
      description: response.description || '',
      plNumber: response.plNumber || '0',
      enabled: response.enabled === 'true' || response.enabled === true
    })
    
  } catch (error) {
    console.error('获取专家种类详情失败:', error)
    showToast('获取详情失败')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      plNumber: formData.plNumber.toString(),
      enabled: formData.enabled.toString()
    }
    
    // 这里应该调用更新专家种类的API
    // await expertApi.updateExpertCategory(route.params.id, submitData)
    
    showSuccessToast('专家种类更新成功')
    
    // 返回详情页面
    router.back()
    
  } catch (error) {
    console.error('更新专家种类失败:', error)
    showToast('更新失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchCategoryDetail()
})

onActivated(() => {
  fetchCategoryDetail()
})
</script>

<style lang="scss" scoped>
.category-edit-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 100px;
  flex-shrink: 0;
}

:deep(.van-field__control) {
  text-align: right;
}
</style>

<route lang="json5">
{
  name: 'ExpertCategoryEdit',
  meta: {
    title: '编辑专家种类'
  }
}
</route>
