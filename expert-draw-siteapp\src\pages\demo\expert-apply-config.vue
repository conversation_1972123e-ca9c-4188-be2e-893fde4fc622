<!-- 使用配置化表单的专家申请页面示例 -->
<template>
  <div class="expert-apply-config-page">
    <van-nav-bar title="专家申请(配置化)" left-arrow @click-left="$router.back()" />
    
    <!-- 头部标题区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-line">成都市农业农村局项目专家抽取</div>
      </div>
    </div>
    
    <!-- 需求部门显示 -->
    <van-cell-group>
      <van-cell
        title="需求部门"
        value="成都市农业农村局"
        label="机关处室/直属单位"
      />
    </van-cell-group>
    
    <!-- 配置化表单 -->
    <div class="form-container">
      <ConfigForm
        v-model="formData"
        :form-config="formConfig"
        :submit-loading="submitLoading"
        submit-text="提交申请"
        @submit="handleSubmit"
        @failed="handleFormFailed"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import ConfigForm from '@/components/common/ConfigForm.vue'
import { useExpertTypes, useServiceRange } from '@/composables/useEnums'

const router = useRouter()

// 使用枚举数据
const { options: expertTypeOptions } = useExpertTypes()
const { options: serviceRangeOptions } = useServiceRange()

// 响应式数据
const submitLoading = ref(false)
const formData = reactive({})

// 表单配置
const formConfig = computed(() => [
  // 项目类型
  {
    type: 'radio',
    label: '项目类型',
    prop: 'type',
    options: [
      { text: '机关', value: 'GOVERNMENT' },
      { text: '直属单位', value: 'DIRECTLY' }
    ],
    props: {
      rules: [{ required: true, message: '请选择项目类型' }],
      direction: 'horizontal',
      defaultValue: 'GOVERNMENT'
    }
  },

  // 专项资金名称
  {
    type: 'input',
    label: '专项资金（项目）名称',
    prop: 'projectName',
    props: {
      placeholder: '请输入专项资金名称',
      rules: [{ required: true, message: '请输入专项资金名称' }],
      maxlength: 100,
      showWordLimit: true
    }
  },

  // 专项资金金额
  {
    type: 'number',
    label: '专项资金金额',
    prop: 'funds',
    props: {
      placeholder: '请输入金额(万元)',
      min: 0,
      step: 0.01,
      rules: [
        { required: true, message: '请输入专项资金金额' },
        { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的金额' }
      ]
    }
  },

  // 项目编号
  {
    type: 'input',
    label: '项目编号',
    prop: 'projectCode',
    props: {
      placeholder: '请输入项目编号',
      rules: [{ required: true, message: '请输入项目编号' }]
    }
  },

  // 专家类型
  {
    type: 'select',
    label: '专家类型',
    prop: 'expertType',
    options: expertTypeOptions.value,
    props: {
      placeholder: '请选择专家类型',
      rules: [{ required: true, message: '请选择专家类型' }]
    }
  },

  // 服务范围
  {
    type: 'select',
    label: '服务范围',
    prop: 'serviceRange',
    options: serviceRangeOptions.value,
    props: {
      placeholder: '请选择服务范围',
      rules: [{ required: true, message: '请选择服务范围' }]
    }
  },

  // 申请人姓名
  {
    type: 'input',
    label: '申请人姓名',
    prop: 'applicantName',
    props: {
      placeholder: '请输入申请人姓名',
      rules: [{ required: true, message: '请输入申请人姓名' }],
      maxlength: 20
    }
  },

  // 申请人电话
  {
    type: 'input',
    label: '申请人电话',
    prop: 'applicantPhone',
    props: {
      placeholder: '请输入申请人电话',
      type: 'tel',
      rules: [
        { required: true, message: '请输入申请人电话' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
      ]
    }
  },

  // 申请日期
  {
    type: 'date',
    label: '申请日期',
    prop: 'applyDate',
    props: {
      placeholder: '请选择申请日期',
      dateType: 'date',
      maxDate: new Date(),
      rules: [{ required: true, message: '请选择申请日期' }]
    }
  },

  // 项目描述
  {
    type: 'textarea',
    label: '项目描述',
    prop: 'description',
    props: {
      placeholder: '请详细描述项目内容、目标和要求',
      rules: [{ required: true, message: '请输入项目描述' }],
      rows: 4,
      maxlength: 500,
      showWordLimit: true
    }
  },

  // 专家要求
  {
    type: 'textarea',
    label: '专家要求',
    prop: 'expertRequirement',
    props: {
      placeholder: '请描述对专家的具体要求',
      rows: 3,
      maxlength: 300,
      showWordLimit: true
    }
  },

  // 备注
  {
    type: 'textarea',
    label: '备注',
    prop: 'remark',
    props: {
      placeholder: '其他需要说明的事项',
      rows: 2,
      maxlength: 200,
      showWordLimit: true
    }
  }
])

// 处理表单提交
const handleSubmit = async (values) => {
  try {
    submitLoading.value = true
    console.log('表单提交数据:', values)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    showSuccessToast('申请提交成功')
    
    // 跳转到申请记录页面
    setTimeout(() => {
      router.push('/user/project-records')
    }, 1500)
    
  } catch (error) {
    console.error('提交申请失败:', error)
    showToast('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

// 处理表单验证失败
const handleFormFailed = (errorInfo) => {
  console.log('表单验证失败:', errorInfo)
  showToast('请检查表单填写')
}
</script>

<style lang="scss" scoped>
.expert-apply-config-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.header-section {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  padding: 20px 16px;
  color: white;
  
  .header-content {
    text-align: center;
    
    .title-line {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }
  }
}

.form-container {
  margin: 16px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.van-cell-group) {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}
</style>

<route lang="json5">
{
  name: 'ExpertApplyConfig',
  meta: {
    title: '专家申请(配置化)'
  }
}
</route>
