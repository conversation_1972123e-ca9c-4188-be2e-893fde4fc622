/**
 * 代理机构相关API接口
 */
export const agencyApi = {
  /**
   * 获取代理机构列表
   * @param {Object} params - 查询参数
   * @param {number} [params.pageSize] - 每页条数，默认20
   * @param {string} [params.keyword] - 搜索关键词（机构名称、联系人、电话）
   * @returns {Promise} 返回代理机构列表数据
   */
  getAgencyList: (params = {}) => {
    return window.$http.fetch('/api/v1/agency', {
      pageSize: 20,
      ...params
    })
  },

  /**
   * 获取代理机构详情
   * @param {string} id - 代理机构ID
   * @returns {Promise} 返回代理机构详情
   */
  getAgencyDetail: (id) => {
    return window.$http.fetch(`/api/v1/agency/${id}`)
  },

  /**
   * 创建代理机构
   * @param {Object} data - 代理机构数据
   * @param {string} data.name - 机构名称
   * @param {string} data.contact - 经办人1
   * @param {string} data.phone - 经办人1电话
   * @param {string} data.contactb - 经办人2
   * @param {string} data.phoneb - 经办人2电话
   * @param {string} data.leader - 负责人
   * @param {string} data.leaderPhone - 负责人电话
   * @param {string} data.serviceRange - 服务范围
   * @returns {Promise} 返回创建结果
   */
  createAgency: (data) => {
    return window.$http.post('/api/v1/agency', data)
  },

  /**
   * 更新代理机构
   * @param {string} id - 代理机构ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  updateAgency: (id, data) => {
    return window.$http.put(`/api/v1/agency/${id}`, data)
  },

  /**
   * 删除代理机构
   * @param {string} id - 代理机构ID
   * @returns {Promise} 返回删除结果
   */
  deleteAgency: (id) => {
    return window.$http.delete(`/api/v1/agency/${id}`)
  },

  /**
   * 启用/禁用代理机构
   * @param {string} id - 代理机构ID
   * @param {boolean} enabled - 是否启用
   * @returns {Promise} 返回操作结果
   */
  toggleAgencyStatus: (id, enabled) => {
    return window.$http.put(`/api/v1/agency/${id}/status`, { enabled })
  }
}
