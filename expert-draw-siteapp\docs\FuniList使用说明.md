# FuniList 通用列表组件使用说明

## 概述

FuniList 是一个高度可配置的通用列表组件，支持搜索、筛选、分页加载、下拉刷新等功能。通过外部配置筛选条件和插槽定义列表内容，可以快速构建各种类型的列表页面。

## 特性

- 🔍 **搜索功能**: 支持关键词搜索
- 🎛️ **筛选功能**: 支持配置化的筛选条件
- 📄 **分页加载**: 支持无限滚动分页
- 🔄 **下拉刷新**: 支持下拉刷新数据
- 🎨 **插槽支持**: 支持自定义头部、列表项、底部内容
- 📱 **移动端优化**: 基于 Vant 组件库，完美适配移动端
- 🔧 **高度可配置**: 支持多种配置选项
- 📑 **Tabs 支持**: 支持多标签页切换，自动判断显示模式

## 基本使用

### 1. 导入组件

```vue
<script setup>
import FuniList from '@/components/FuniList.vue'
</script>
```

### 2. 定义数据加载函数

```javascript
const loadDataFunction = async (params) => {
  try {
    // params 包含: page, pageSize, keyword, 筛选条件, extraParams
    const response = await api.getList(params)

    // 返回标准格式
    return {
      data: response.data,    // 数据数组
      total: response.total   // 总数
    }

    // 或者直接返回数组
    // return response.data
  } catch (error) {
    throw error
  }
}
```

### 3. 配置 Tabs

**重要**: FuniList 组件基于 tabs 设计，即使是单个列表也需要配置为一个 tab。

```javascript
const tabsConfig = [
  {
    key: 'list',                    // 必需：Tab 的唯一标识
    title: '列表',                  // 必需：Tab 显示的标题
    loadFunction: loadDataFunction, // 必需：数据加载函数
    searchPlaceholder: '请输入搜索关键词', // 可选：搜索框占位符
    keyword: 'keyword',             // 可选：搜索参数名，默认 'keyword'
    filterConfig: [],               // 可选：筛选配置
    extraParams: {}                 // 可选：额外查询参数
  }
]
```

### 4. 使用组件

```vue
<template>
  <FuniList
    :tabs="tabsConfig"
    item-key="id"
    @item-click="handleItemClick"
  >
    <!-- 自定义列表项 -->
    <template #item="{ item }">
      <div class="custom-item">
        <h3>{{ item.title }}</h3>
        <p>{{ item.description }}</p>
      </div>
    </template>
  </FuniList>
</template>
```

## Props 配置

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| tabs | **必需** Tabs 配置数组 | Array | [] |
| itemKey | 列表项的唯一标识字段 | String | 'id' |
| pageSize | 分页大小 | Number | 20 |
| finishedText | 完成加载文本 | String | '没有更多了' |
| emptyText | 空状态文本 | String | '暂无数据' |
| emptyImage | 空状态图片 | String | 'search' |
| autoLoad | 是否自动加载 | Boolean | true |
| extraParams | 额外的查询参数 | Object | {} |
| defaultActiveTab | 默认激活的 tab | String | '' |
| tabsSticky | tabs 是否粘性定位 | Boolean | false |
| tabsOffsetTop | tabs 粘性定位偏移量 | Number | 0 |
| tabsSwipeable | tabs 是否支持手势滑动 | Boolean | true |
| tabsAnimated | tabs 是否开启切换动画 | Boolean | true |
| itemClass | 列表项的CSS类名 | String | '' |
| pageSize | 分页大小 | Number | 20 |
| finishedText | 完成加载文本 | String | '没有更多了' |
| emptyText | 空状态文本 | String | '暂无数据' |
| emptyImage | 空状态图片 | String | 'search' |
| autoLoad | 是否自动加载 | Boolean | true |
| extraParams | 额外的查询参数 | Object | {} |

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| item-click | 列表项点击事件 | (item, index) |
| filter-change | 筛选条件变化事件 | (filterData) |
| search-change | 搜索关键词变化事件 | (keyword) |
| load-success | 数据加载成功事件 | (result, listData) |
| load-error | 数据加载失败事件 | (error) |
| tab-change | Tab 切换事件 | (tabKey, tabConfig) |

## 插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| header | 自定义头部内容 | - |
| item | 自定义列表项内容 | { item, index } |
| footer | 自定义底部内容 | - |

## 筛选配置

筛选配置使用与 FuniForm 相同的配置格式：

```javascript
const filterConfig = [
  {
    type: 'input',
    label: '姓名',
    prop: 'name',
    props: {
      placeholder: '请输入姓名',
      clearable: true
    }
  },
  {
    type: 'select',
    label: '类型',
    prop: 'type',
    options: [
      { text: '类型1', value: 'type1' },
      { text: '类型2', value: 'type2' }
    ],
    props: {
      placeholder: '请选择类型'
    }
  },
  {
    type: 'date',
    label: '日期',
    prop: 'date',
    props: {
      placeholder: '请选择日期',
      dateType: 'date'
    }
  }
]
```

## 组件方法

通过 ref 可以调用以下方法：

```javascript
const listRef = ref()

// 刷新列表
listRef.value.refresh()

// 加载更多
listRef.value.loadMore()

// 重置筛选
listRef.value.resetFilter()

// 获取列表数据
const listData = listRef.value.getListData()

// 获取筛选数据
const filterData = listRef.value.getFilterData()
```

## 完整示例

```vue
<template>
  <div class="page">
    <FuniList
      ref="listRef"
      :load-function="loadUserList"
      :filter-config="userFilterConfig"
      search-placeholder="搜索用户姓名"
      item-key="id"
      :extra-params="{ status: 'active' }"
      @item-click="handleUserClick"
      @filter-change="handleFilterChange"
    >
      <!-- 自定义头部 -->
      <template #header>
        <van-notice-bar text="用户列表" />
      </template>

      <!-- 自定义列表项 -->
      <template #item="{ item, index }">
        <div class="user-item">
          <div class="user-avatar">
            <van-image :src="item.avatar" round />
          </div>
          <div class="user-info">
            <div class="user-name">{{ item.name }}</div>
            <div class="user-email">{{ item.email }}</div>
            <div class="user-status">
              <van-tag :type="item.status === 'active' ? 'success' : 'default'">
                {{ item.status === 'active' ? '活跃' : '非活跃' }}
              </van-tag>
            </div>
          </div>
        </div>
      </template>

      <!-- 自定义底部 -->
      <template #footer>
        <div class="footer-actions">
          <van-button type="primary" block @click="addUser">
            添加用户
          </van-button>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { showToast } from 'vant'
import FuniList from '@/components/FuniList.vue'
import { userApi } from '@/api/user'

const listRef = ref()

// 筛选配置
const userFilterConfig = [
  {
    type: 'input',
    label: '姓名',
    prop: 'name',
    props: {
      placeholder: '请输入用户姓名',
      clearable: true
    }
  },
  {
    type: 'select',
    label: '状态',
    prop: 'status',
    options: [
      { text: '全部', value: '' },
      { text: '活跃', value: 'active' },
      { text: '非活跃', value: 'inactive' }
    ],
    props: {
      placeholder: '请选择状态'
    }
  },
  {
    type: 'date',
    label: '注册日期',
    prop: 'registerDate',
    props: {
      placeholder: '请选择注册日期',
      dateType: 'date'
    }
  }
]

// 加载用户列表
const loadUserList = async (params) => {
  try {
    const response = await userApi.getUserList(params)
    return {
      data: response.data,
      total: response.total
    }
  } catch (error) {
    throw error
  }
}

// 处理用户点击
const handleUserClick = (user, index) => {
  showToast(`点击了用户: ${user.name}`)
}

// 处理筛选变化
const handleFilterChange = (filterData) => {
  console.log('筛选条件:', filterData)
}

// 添加用户
const addUser = () => {
  showToast('添加用户功能')
}
</script>

<style lang="scss" scoped>
.page {
  height: 100vh;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;

  .user-avatar {
    width: 48px;
    height: 48px;
  }

  .user-info {
    flex: 1;

    .user-name {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .user-email {
      font-size: 14px;
      color: #646566;
      margin-bottom: 8px;
    }
  }
}

.footer-actions {
  padding: 16px;
}
</style>
```

## Tabs 功能详解

### Tabs 显示逻辑

FuniList 组件会根据 `tabs` 配置数组的长度自动判断显示模式：

- **单个 Tab 或无 Tab**: 当 `tabs.length <= 1` 时，不显示 Tabs 切换界面，直接显示列表内容
- **多个 Tabs**: 当 `tabs.length > 1` 时，显示 Tabs 切换界面，支持多标签页切换

### Tabs 配置格式

```javascript
const tabsConfig = [
  {
    key: 'tab1',                    // 必需：Tab 的唯一标识
    title: '标签1',                 // 必需：Tab 显示的标题
    loadFunction: loadTab1Data,     // 必需：该 Tab 的数据加载函数
    searchPlaceholder: '请输入搜索关键词', // 可选：搜索框占位符
    keyword: 'keyword',             // 可选：搜索参数名，默认 'keyword'
    filterConfig: [],               // 可选：该 Tab 的筛选配置
    extraParams: { status: 'active' }, // 可选：该 Tab 的额外查询参数
    badge: '5',                     // 可选：Tab 右上角的徽标
    dot: false,                     // 可选：是否显示小红点
    disabled: false                 // 可选：是否禁用该 Tab
  }
]
```

### 单个列表示例

```vue
<template>
  <FuniList
    :tabs="singleTabConfig"
    item-key="id"
    @item-click="handleItemClick"
  >
    <template #item="{ item }">
      <div class="item-card">
        <h3>{{ item.name }}</h3>
        <p>{{ item.description }}</p>
      </div>
    </template>
  </FuniList>
</template>

<script setup>
import FuniList from '@/components/FuniList.vue'

// 单个列表配置
const singleTabConfig = [
  {
    key: 'list',
    title: '列表',
    loadFunction: loadListData,
    searchPlaceholder: '搜索内容',
    keyword: 'keyword',
    filterConfig: [
      {
        type: 'select',
        label: '状态',
        prop: 'status',
        options: [
          { text: '全部', value: '' },
          { text: '启用', value: 'active' },
          { text: '禁用', value: 'inactive' }
        ]
      }
    ]
  }
]

async function loadListData(params) {
  const response = await api.getList(params)
  return {
    data: response.data,
    total: response.total
  }
}
</script>
```

### 多标签列表示例

```vue
<template>
  <FuniList
    :tabs="multiTabsConfig"
    @item-click="handleRecordClick"
    @tab-change="handleTabChange"
  >
    <template #item="{ item: record }">
      <div class="record-item">
        <div class="record-title">
          <span class="title">{{ record.name }}</span>
          <span class="status">{{ record.status }}</span>
        </div>
        <div class="record-info">
          <div>申请人: {{ record.applicant }}</div>
          <div>申请时间: {{ record.createTime }}</div>
        </div>
      </div>
    </template>
  </FuniList>
</template>

<script setup>
import FuniList from '@/components/FuniList.vue'

const multiTabsConfig = [
  {
    key: 'APPLY',
    title: '已发起',
    loadFunction: (params) => api.getRecords({ ...params, status: 'APPLY' }),
    searchPlaceholder: '搜索项目名称/编号',
    keyword: 'keyword',
    extraParams: { status: 'APPLY' }
  },
  {
    key: 'TODO',
    title: '待审批',
    loadFunction: (params) => api.getRecords({ ...params, status: 'TODO' }),
    searchPlaceholder: '搜索项目名称/编号',
    keyword: 'keyword',
    extraParams: { status: 'TODO' }
  },
  {
    key: 'DONE',
    title: '已审批',
    loadFunction: (params) => api.getRecords({ ...params, status: 'DONE' }),
    searchPlaceholder: '搜索项目名称/编号',
    keyword: 'keyword',
    extraParams: { status: 'DONE' }
  }
]
</script>
```

## 注意事项

1. **数据加载函数**: 必须返回包含 `data` 和 `total` 的对象，或直接返回数组
2. **筛选配置**: 使用与 FuniForm 相同的配置格式
3. **插槽使用**: 通过插槽可以完全自定义列表项的显示内容
4. **事件处理**: 通过事件可以监听用户的各种操作
5. **组件方法**: 通过 ref 可以调用组件的各种方法
6. **响应式**: 组件会自动响应 `extraParams` 的变化并重新加载数据
7. **Tabs 自动判断**: 组件会根据 `tabs` 数组长度自动判断是否显示 Tabs 切换
8. **数据隔离**: 每个 Tab 的数据是独立的，切换 Tab 不会影响其他 Tab 的数据
