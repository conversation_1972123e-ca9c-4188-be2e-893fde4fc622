<!-- 申请进度查看页面 -->
<template>
  <div class="progress-page">
    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical>
      加载中...
    </van-loading>

    <!-- 进度内容 -->
    <div v-else-if="progressData" class="progress-container">
      <!-- 进度步骤 -->
      <div class="steps-section">
        <div class="custom-steps" v-if="progressData.flows.length > 0">
          <div
            class="step-item"
            v-for="(step, index) in progressData.flows"
            :key="index"
            :class="{ active: step.activity }"
          >
            <div class="step-icon" :class="{ active: step.activity }">
              {{ index + 1 }}
            </div>
            <div class="step-title">{{ step.conf.flowNode }}</div>
            <div
              v-if="index < progressData.flows.length - 1"
              class="step-line"
              :class="{ active: step.activity && index < currentStepIndex }"
            ></div>
          </div>
        </div>
        <div v-else class="no-steps">暂无流程步骤信息</div>
      </div>

      <!-- 动态生成的审批节点 -->
      <div
        v-for="(flow, index) in progressData.flows"
        :key="index"
        class="section"
      >
        <div class="section-title">{{ getFlowNodeTitle(flow) }}</div>

        <!-- 审批人信息 -->
        <van-cell-group>
          <van-cell
            :title="getApproverTitle(flow)"
            :value="getApproverName(flow)"
          />
          <van-cell
            v-if="getApproverContact(flow)"
            title="联系方式"
            :value="getApproverContact(flow)"
          />
          <van-cell
            v-if="getApproverPost(flow)"
            title="任职岗位"
            :value="getApproverPost(flow)"
          />
          <van-cell
            v-if="getApproverDept(flow)"
            title="任职处室"
            :value="getApproverDept(flow)"
          />
          <van-cell
            v-if="isFirstFlow(index)"
            title="需求申请时间"
            :value="formatDateTime(progressData.createTime)"
          />
        </van-cell-group>
      </div>

      <!-- 审批意见 -->
      <van-form ref="approvalFormRef" required="auto">
        <van-field
          name="approvalStatus"
          label="审批意见"
          :rules="[{ required: true, message: '请选择审批意见' }]"
        >
          <template #input>
            <van-radio-group
              v-model="approvalForm.approvalStatus"
              direction="horizontal"
            >
              <van-radio name="PASS">通过</van-radio>
              <van-radio name="REJECT">驳回</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-model="approvalForm.approvalContent"
          name="approvalContent"
          label="补充说明"
          type="textarea"
          placeholder="请输入补充说明"
          rows="4"
          maxlength="200"
          show-word-limit
          :rules="[{ required: true, message: '请输入补充说明' }]"
          class="comment-field"
        />
      </van-form>

      <!-- 项目详情 -->
      <div class="section">
        <div class="section-header">
          <span class="project-code">{{
            progressData.code || "99300004-2"
          }}</span>
          <span class="project-title">{{ getProjectTitle() }}</span>
        </div>

        <van-cell-group>
          <van-cell
            title="项目编号"
            :value="progressData.code || '99300004-2'"
          />
          <van-cell
            title="需求部门（机关处室/直属单位）"
            :value="progressData.department || progressData.area?.name || '-'"
          />
          <van-cell
            title="部门专项资金（项目）名称"
            :value="progressData.name || '-'"
          />
          <van-cell
            title="项目专项资金金额"
            :value="progressData.funds ? `${progressData.funds}万元` : '-'"
          />
          <van-cell title="评审开始时间" :value="getReviewTimeInfo()" />
          <van-cell title="评审地点" :value="progressData.reviewPlace || '-'" />
        </van-cell-group>

        <!-- 查看全部按钮 -->
        <div class="view-all-section">
          <van-button
            type="primary"
            plain
            size="small"
            @click="toggleExpertDetail"
          >
            {{ showExpertDetail ? "收起" : "查看全部" }}
          </van-button>
        </div>
      </div>

      <!-- 评审专家人员建议详情 -->
      <div v-if="showExpertDetail" class="expert-detail-section">
        <div class="section-title">评审专家人员建议</div>

        <!-- 抽取专家类型 -->
        <van-cell-group>
          <van-cell
            title="抽取专家"
            :value="getExpertSourceText(progressData.proposalExpertSources)"
          />
        </van-cell-group>

        <!-- 专家类型和人数 -->
        <div class="expert-types">
          <div class="section-header">专家行业类别及人数</div>

          <div
            v-if="progressData.planDtos && progressData.planDtos.length > 0"
            class="expert-list"
          >
            <div
              class="expert-type-item"
              v-for="(item, index) in progressData.planDtos"
              :key="index"
            >
              <div class="type-row">
                <span class="type-label">{{
                  item.expertType?.name || "-"
                }}</span>
                <span class="type-count">{{ item.expertNum }}人</span>
              </div>
            </div>
          </div>
          <div v-else class="no-data">暂无专家类型配置</div>
        </div>

        <!-- 统计信息 -->
        <div class="expert-stats">
          <van-cell-group>
            <van-cell
              title="抽取专家人数"
              :value="`${progressData.planExpertPlNum || 0}人`"
            />
            <van-cell
              title="经办处室或直属单位人数"
              :value="`${progressData.departmentPlNum || 0}人`"
            />
            <van-cell
              title="邀请专家人数"
              :value="`${progressData.inviteExpertPlNum || 0}人`"
            />
            <van-cell
              title="审批领导"
              :value="progressData.approvalLeader || '-'"
            />
          </van-cell-group>
        </div>

        <!-- 补充说明 -->
        <div class="description-section">
          <van-cell-group>
            <van-cell
              title="补充说明"
              :value="progressData.description || '无'"
              class="description-cell"
            />
          </van-cell-group>
        </div>

        <!-- 申请人信息 -->
        <div class="applicant-section">
          <van-cell-group>
            <van-cell
              title="需求申请人姓名"
              :value="progressData.applyUserName || '-'"
            />
            <van-cell
              title="需求申请人联系方式"
              :value="progressData.applyUserPhone || '-'"
            />
          </van-cell-group>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <van-empty description="加载失败，请重试" />
      <van-button type="primary" @click="loadProgress">重新加载</van-button>
    </div>

    <!-- 底部操作按钮 -->
    <div v-if="progressData" class="bottom-actions">
      <van-button class="back-btn" @click="goBack"> 返回 </van-button>
      <van-button
        type="primary"
        class="submit-btn"
        :loading="submitting"
        :disabled="submitting"
        @click="submitProgress"
      >
        {{ submitting ? "提交中..." : "提交审批" }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import { expertApi } from "@/api/expert";
import moment from "moment";

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const progressData = ref(null);
const showExpertDetail = ref(false);
const approvalFormRef = ref(null);
const submitting = ref(false);

// 审批表单数据
const approvalForm = ref({
  approvalStatus: "",
  approvalContent: "",
});

// 当前流程步骤索引
const currentStepIndex = computed(() => {
  return getCurrentFlowIndex();
});

// 获取当前流程索引
const getCurrentFlowIndex = () => {
  if (!progressData.value?.flows) return 0;

  const flows = progressData.value.flows;

  // 找到第一个未完成的流程
  for (let i = 0; i < flows.length; i++) {
    const flow = flows[i];
    if (!flow.approvalStatus || flow.approvalStatus === "APPROVAL") {
      return i;
    }
  }

  // 如果所有流程都完成了，返回最后一个
  return flows.length - 1;
};

// 获取进度数据
const loadProgress = async () => {
  try {
    loading.value = true;
    const id = route.params.id;
    if (!id) {
      showToast("缺少申请ID");
      router.back();
      return;
    }

    const response = await expertApi.getApplyDetail(id);
    progressData.value = response.data;
  } catch (error) {
    console.error("加载进度失败:", error);
    showToast("加载进度失败");
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return "-";
  return moment(dateStr).format("YYYY-MM-DD HH:mm:ss");
};

// 获取流程节点标题
const getFlowNodeTitle = (flow) => {
  return flow.conf?.flowNode || flow.conf?.name || "审批节点";
};

// 获取审批人标题
const getApproverTitle = (flow) => {
  const flowNode = flow.conf?.flowNode || "";
  if (flowNode.includes("经办")) {
    return "经办人姓名";
  } else if (flowNode.includes("部门") || flowNode.includes("负责人")) {
    return "审批人";
  } else if (flowNode.includes("审计") || flowNode.includes("监督")) {
    return "审批人";
  }
  return "审批人";
};

// 获取审批人姓名
const getApproverName = (flow) => {
  // 对于经办人受理节点，使用flowUser
  const flowNode = flow.conf?.flowNode || "";
  if (flowNode.includes("经办")) {
    return progressData.value?.flowUser?.nickname || "-";
  }
  // 其他节点使用approvalUser
  return flow.approvalUser?.nickname || "-";
};

// 获取审批人联系方式
const getApproverContact = (flow) => {
  const flowNode = flow.conf?.flowNode || "";
  if (flowNode.includes("经办")) {
    return progressData.value?.flowUser?.phone || "";
  }
  return flow.approvalUser?.phone || "";
};

// 获取审批人岗位
const getApproverPost = (flow) => {
  const flowNode = flow.conf?.flowNode || "";
  if (flowNode.includes("经办")) {
    return progressData.value?.flowUser?.post || "";
  }
  return flow.approvalUser?.post || "";
};

// 获取审批人部门
const getApproverDept = (flow) => {
  const flowNode = flow.conf?.flowNode || "";
  if (flowNode.includes("经办")) {
    return progressData.value?.flowUser?.groups?.[0]?.name || "";
  }
  return flow.approvalUser?.groups?.[0]?.name || "";
};

// 判断是否为第一个流程（显示申请时间）
const isFirstFlow = (index) => {
  return index === 0;
};

// 判断是否有审批结果
const hasApprovalResult = (flow) => {
  return flow.approvalStatus === "PASS" || flow.approvalStatus === "REJECT";
};

// 获取专家来源文本
const getExpertSourceText = (source) => {
  const sourceMap = {
    OUR: "我局专家库",
    PROVINCIAL: "省财政厅专家库",
  };
  return sourceMap[source] || "-";
};

// 获取部门审批状态（用于初始化表单）
const getDepartmentApprovalStatus = () => {
  const flows = progressData.value?.flows || [];
  const deptFlow = flows.find((flow) => flow.conf?.flowNode?.includes("部门"));
  return deptFlow?.approvalStatus || "";
};

// 获取部门审批意见（用于初始化表单）
const getDepartmentApprovalComment = () => {
  const flows = progressData.value?.flows || [];
  const deptFlow = flows.find((flow) => flow.conf?.flowNode?.includes("部门"));
  return deptFlow?.approvalContent || "";
};

// 获取项目标题
const getProjectTitle = () => {
  return `【这里是名称这里是名称这里是名称】评审专家库抽取申请表`;
};

// 获取评审时间信息
const getReviewTimeInfo = () => {
  const reviewDate = progressData.value?.reviewDate;
  const dateNum = progressData.value?.dateNum;

  if (!reviewDate) return "-";

  const formattedDate = moment(reviewDate).format("YYYY-MM-DD HH:mm:ss");
  const duration = dateNum ? ` 预计${dateNum}天` : "";

  return `${formattedDate}${duration}`;
};

// 切换专家详情显示
const toggleExpertDetail = () => {
  showExpertDetail.value = !showExpertDetail.value;
};

// 返回
const goBack = () => {
  router.back();
};

// 提交审批（底部按钮）
const submitProgress = async () => {
  try {
    // 表单验证
    await approvalFormRef.value?.validate();

    submitting.value = true;
    const flow = progressData.value.flows.find((x) => x.activity);
    // 构造审批数据
    const approvalData = {
      id: flow.id,
      approvalStatus: approvalForm.value.approvalStatus,
      approvalContent: approvalForm.value.approvalContent,
    };

    // 调用审批接口
    await expertApi.submitApproval(flow.id, approvalData);

    showToast("审批提交成功");

    // 延迟跳转到结果页面
    setTimeout(() => {
      router.replace('/user/expert-apply/result');
    }, 1500);
  } catch (error) {
    console.error("审批提交失败:", error);
    if (error.message) {
      showToast(error.message);
    } else {
      showToast("审批提交失败，请重试");
    }
  } finally {
    submitting.value = false;
  }
};

// 页面初始化
onMounted(() => {
  loadProgress();
});
</script>

<style lang="scss" scoped>
.progress-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #969799;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  padding: 20px;

  .van-button {
    margin-top: 16px;
  }
}

.progress-container {
  padding: 16px;
}

.steps-section {
  background: white;
  padding: 20px 16px;
  margin-bottom: 12px;
  border-radius: 8px;
}

.custom-steps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;

  &:not(:last-child) {
    margin-right: 16px;
  }
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ebedf0;
  color: #969799;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
  z-index: 2;
  position: relative;

  &.active {
    background: #07c160;
    color: white;
  }
}

.step-title {
  font-size: 12px;
  color: #646566;
  text-align: center;
  line-height: 1.2;
}

.step-line {
  position: absolute;
  top: 12px;
  left: calc(50% + 12px);
  right: calc(-50% + 12px);
  height: 1px;
  background: #ebedf0;
  z-index: 1;

  &.active {
    background: #07c160;
  }
}

.step-item:last-child .step-line {
  display: none;
}

.no-steps {
  text-align: center;
  padding: 20px;
  color: #969799;
  font-size: 14px;
}

.section {
  background: white;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  background: #fafafa;
}

.section-header {
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  background: #fafafa;

  .project-code {
    display: block;
    font-size: 14px;
    color: #646566;
    margin-bottom: 4px;
  }

  .project-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    line-height: 1.4;
  }
}

.approval-section {
  &.readonly {
    background: #fafafa;
  }

  .approval-opinion {
    margin-bottom: 16px;

    .opinion-label {
      font-size: 14px;
      color: #323233;
      margin-bottom: 12px;
      font-weight: 500;

      .required {
        color: #ff4d4f;
        margin-left: 2px;
      }
    }

    .opinion-radios {
      display: flex;
      gap: 24px;

      .opinion-radio {
        font-size: 14px;

        :deep(.van-radio__label) {
          color: #323233;
          margin-left: 8px;
        }

        :deep(.van-radio__icon--checked) {
          .van-icon {
            background: #07c160;
            border-color: #07c160;
          }
        }
      }
    }
  }

  .approval-result {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .result-label {
      font-size: 14px;
      color: #323233;
      font-weight: 500;
      margin-right: 12px;
    }

    .result-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;

      &.pass {
        background: #f0f9ff;
        color: #07c160;
        border: 1px solid #07c160;
      }

      &.reject {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ff4d4f;
      }
    }
  }

  .approval-comment {
    .comment-field {
      :deep(.van-field__label) {
        font-size: 14px;
        color: #323233;
        font-weight: 500;

        &::after {
          content: "*";
          color: #ff4d4f;
          margin-left: 2px;
        }
      }

      :deep(.van-field__control) {
        font-size: 14px;
        line-height: 1.5;
      }

      :deep(.van-field__word-limit) {
        color: #969799;
        font-size: 12px;
      }
    }

    &.readonly {
      .comment-label {
        font-size: 14px;
        color: #323233;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .comment-content {
        padding: 12px;
        background: white;
        border-radius: 6px;
        font-size: 14px;
        color: #646566;
        line-height: 1.5;
        border: 1px solid #ebedf0;
      }
    }
  }
}

.view-all-section {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #f7f8fa;
}

.expert-detail-section {
  background: white;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;

  .section-header {
    font-size: 14px;
    font-weight: 500;
    color: #323233;
    padding: 16px;
    background: white;
    border-bottom: 1px solid #f7f8fa;
  }

  .expert-list {
    background: white;

    .expert-type-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f7f8fa;

      &:last-child {
        border-bottom: none;
      }

      .type-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .type-label {
          font-size: 14px;
          color: #323233;
        }

        .type-count {
          font-size: 14px;
          color: #07c160;
          font-weight: 500;
        }
      }
    }
  }

  .no-data {
    padding: 20px;
    text-align: center;
    color: #969799;
    font-size: 14px;
    background: white;
  }

  .expert-stats,
  .description-section,
  .applicant-section {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .description-cell {
    :deep(.van-cell__value) {
      text-align: left;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #ebedf0;
  display: flex;
  gap: 12px;
  z-index: 100;

  .back-btn {
    flex: 1;
    height: 44px;
    border: 1px solid #ebedf0;
    background: white;
    color: #646566;
  }

  .submit-btn {
    flex: 1;
    height: 44px;
    background: #07c160;
    border: none;
  }
}

// 覆盖vant样式
:deep(.van-cell) {
  padding: 12px 16px;

  .van-cell__title {
    font-size: 14px;
    color: #323233;
  }

  .van-cell__value {
    font-size: 14px;
    color: #646566;
  }
}
</style>
