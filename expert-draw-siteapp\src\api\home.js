/**
 * 首页相关API接口
 */
export const homeApi = {
  /**
   * 获取首页统计数据
   * @returns {Promise} 返回首页统计信息
   */
  getHomeStats: () => {
    return window.$http.fetch('/api/home/<USER>')
  },

  /**
   * 获取专家申请列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @returns {Promise} 返回专家申请列表
   */
  getExpertApplications: (params) => {
    return window.$http.fetch('/api/expert/applications', params)
  },

  /**
   * 获取代理机构申请列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @returns {Promise} 返回代理机构申请列表
   */
  getAgencyApplications: (params) => {
    return window.$http.fetch('/api/agency/applications', params)
  },

  /**
   * 获取项目申报记录
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.projectName] - 项目名称（可选）
   * @param {string} [params.status] - 状态（可选）
   * @returns {Promise} 返回项目申报记录列表
   */
  getProjectRecords: (params) => {
    return window.$http.fetch('/api/project/records', params)
  },

  /**
   * 获取专家资料库
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.expertName] - 专家姓名（可选）
   * @param {string} [params.field] - 专业领域（可选）
   * @returns {Promise} 返回专家资料列表
   */
  getExpertDatabase: (params) => {
    return window.$http.fetch('/api/expert/database', params)
  },

  /**
   * 提交专家抽取申请
   * @param {Object} data - 申请数据
   * @param {string} data.projectName - 项目名称
   * @param {string} data.projectType - 项目类型
   * @param {number} data.expertCount - 需要专家数量
   * @param {string} data.field - 专业领域
   * @param {string} data.description - 项目描述
   * @returns {Promise} 返回申请结果
   */
  submitExpertApplication: (data) => {
    return window.$http.post('/api/expert/apply', data)
  },

  /**
   * 提交代理机构抽取申请
   * @param {Object} data - 申请数据
   * @param {string} data.projectName - 项目名称
   * @param {string} data.projectType - 项目类型
   * @param {number} data.agencyCount - 需要代理机构数量
   * @param {string} data.businessType - 业务类型
   * @param {string} data.description - 项目描述
   * @returns {Promise} 返回申请结果
   */
  submitAgencyApplication: (data) => {
    return window.$http.post('/api/agency/apply', data)
  }
}

/**
 * 管理端首页相关API接口
 */
export const govHomeApi = {
  /**
   * 获取管理端首页统计数据
   * @returns {Promise} 返回管理端首页统计信息
   */
  getGovHomeStats: () => {
    return window.$http.fetch('/api/gov/home/<USER>')
  },

  /**
   * 获取代理机构管理统计
   * @returns {Promise} 返回代理机构统计数据
   */
  getAgencyStats: () => {
    return window.$http.fetch('/api/gov/agency/stats')
  },

  /**
   * 获取专家库管理统计
   * @returns {Promise} 返回专家库统计数据
   */
  getExpertStats: () => {
    return window.$http.fetch('/api/gov/expert/stats')
  },

  /**
   * 获取采购项目管理统计
   * @returns {Promise} 返回采购项目统计数据
   */
  getProjectStats: () => {
    return window.$http.fetch('/api/gov/project/stats')
  },

  /**
   * 获取名单管理统计
   * @returns {Promise} 返回名单管理统计数据
   */
  getListStats: () => {
    return window.$http.fetch('/api/gov/list/stats')
  }
}