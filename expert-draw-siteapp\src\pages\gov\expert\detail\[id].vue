<!-- 专家详情页面 -->
<template>
  <div class="expert-detail-page">
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>
    
    <div v-else-if="expertDetail" class="detail-content">
      <!-- 基本信息 -->
      <van-cell-group inset title="基本信息">
        <van-cell title="专家姓名" :value="expertDetail.name" />
        <van-cell title="性别" :value="getGenderText(expertDetail.gender)" />
        <van-cell title="出生年月" :value="formatBirthday(expertDetail.birthday)" />
        <van-cell title="身份证号" :value="expertDetail.idNum" />
        <van-cell title="联系电话" :value="expertDetail.phone" />
        <van-cell title="状态">
          <template #value>
            <van-tag :type="expertDetail.enabled ? 'success' : 'default'">
              {{ expertDetail.enabled ? '已启用' : '已禁用' }}
            </van-tag>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 专业信息 -->
      <van-cell-group inset title="专业信息">
        <van-cell title="专家类型" :value="expertDetail.type?.name || '未分类'" />
        <van-cell title="单位职务" :value="expertDetail.post || '暂无'" />
        <van-cell title="所在领域" :value="expertDetail.field || '暂无'" />
        <van-cell title="专业级别" :value="expertDetail.level || '暂无'" />
        <van-cell title="服务范围" :value="getServiceRangeText(expertDetail.serviceRange)" />
      </van-cell-group>

      <!-- 时间信息 -->
      <van-cell-group inset title="时间信息">
        <van-cell title="创建时间" :value="formatDateTime(expertDetail.createTime)" />
        <van-cell title="修改时间" :value="formatDateTime(expertDetail.modifyTime)" />
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-section">
        <van-button
          type="primary"
          size="large"
          block
          @click="handleEdit"
        >
          编辑
        </van-button>
        
        <van-button
          :type="expertDetail.enabled ? 'warning' : 'success'"
          size="large"
          block
          @click="handleToggleStatus"
          :loading="statusLoading"
        >
          {{ expertDetail.enabled ? '禁用' : '启用' }}
        </van-button>
        
        <van-button
          type="danger"
          size="large"
          block
          @click="handleDelete"
          :loading="deleteLoading"
        >
          删除
        </van-button>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-else
      description="专家不存在"
      image="error"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { expertApi } from '@/api/expert'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const statusLoading = ref(false)
const deleteLoading = ref(false)
const expertDetail = ref(null)

// 方法
const fetchExpertDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    const response = await expertApi.getExpertDetail(id)
    expertDetail.value = response
  } catch (error) {
    console.error('获取专家详情失败:', error)
    showToast('获取详情失败')
    
    // 使用模拟数据
    expertDetail.value = {
      id: route.params.id,
      type: { id: '1', name: '农业测试类' },
      name: '张三',
      gender: 'MALE',
      birthday: '1978-12-12',
      idNum: '0101**********0029',
      post: '高级工程师',
      field: '农业',
      level: '高级',
      phone: '138****5678',
      enabled: true,
      serviceRange: 'ALL',
      createTime: '2024-01-15T10:30:00',
      modifyTime: '2024-01-15T10:30:00'
    }
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  router.push(`/gov/expert/edit/${route.params.id}`)
}

const handleToggleStatus = async () => {
  try {
    const action = expertDetail.value.enabled ? '禁用' : '启用'
    await showConfirmDialog({
      title: '确认操作',
      message: `确定要${action}该专家吗？`
    })
    
    statusLoading.value = true
    const newStatus = !expertDetail.value.enabled
    
    await expertApi.toggleExpertStatus(route.params.id, newStatus)
    
    expertDetail.value.enabled = newStatus
    showSuccessToast(`${action}成功`)
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换状态失败:', error)
      showToast('操作失败，请重试')
    }
  } finally {
    statusLoading.value = false
  }
}

const handleDelete = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '删除后无法恢复，确定要删除该专家吗？'
    })
    
    deleteLoading.value = true
    await expertApi.deleteExpert(route.params.id)
    
    showSuccessToast('删除成功')
    router.back()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      showToast('删除失败，请重试')
    }
  } finally {
    deleteLoading.value = false
  }
}

const getGenderText = (gender) => {
  return gender === 'MALE' ? '男' : gender === 'FEMALE' ? '女' : '未知'
}

const formatBirthday = (birthday) => {
  if (!birthday) return ''
  return birthday.replace(/\//g, '-')
}

const getServiceRangeText = (range) => {
  const rangeMap = {
    'ALL': '全部',
    'DIRECTLY': '局机关和直属单位',
    'DISTRICT': '区县'
  }
  return rangeMap[range] || range
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  fetchExpertDetail()
})

onActivated(() => {
  fetchExpertDetail()
})
</script>

<style lang="scss" scoped>
.expert-detail-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  .van-cell-group {
    margin-bottom: 16px;
  }
}

.action-section {
  margin-top: 32px;
  
  .van-button {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

<route lang="json5">
{
  name: 'ExpertDetail',
  meta: {
    title: '专家详情'
  }
}
</route>
