# 房联移动端前端项目 - AI 开发规范文档

## 1. 项目概述

### 1.1 项目简介
房联（funi）移动端前端项目是一个基于 Vue 3 + Vite + Vant 的移动端前端应用，专为房联业务场景设计。项目采用现代化的前端技术栈，提供高性能的移动端用户体验。

### 1.2 核心技术栈
- **前端框架**: Vue 3.5.17 (Composition API)
- **构建工具**: Vite 7.0.3
- **UI 组件库**: Vant 4.9.20 (移动端专用 UI 库)
- **路由管理**: Vue Router 4.5.1 + unplugin-vue-router (文件系统路由)
- **状态管理**: Pinia 3.0.3 + pinia-plugin-persistedstate (持久化支持)
- **样式处理**: SCSS + PostCSS (移动端适配)
- **HTTP 请求**: @funi-lib/utils (房联自研工具库)
- **包管理器**: pnpm
- **开发服务器**: 默认端口 8000

### 1.3 项目特色
- 🚀 基于 Vite 的快速开发体验
- 📱 专为移动端优化的响应式设计 (375px 基准，最大 600px)
- 🎨 完整的 Vant UI 组件库集成
- 🗂️ 文件系统路由，开发更直观
- 💾 状态持久化，用户体验更佳
- 🔧 自动组件注册，减少样板代码
- 🔐 内置加密和认证机制

## 2. 代码结构和组织

### 2.1 目录结构
详情见文档：[项目结构](docs/项目结构.md)


### 2.2 命名约定
- **页面文件**: kebab-case，如 `user-profile.vue`
- **组件文件**: PascalCase，如 `UserCard.vue`
- **工具文件**: camelCase，如 `formatDate.js`
- **常量文件**: UPPER_SNAKE_CASE，如 `API_CONSTANTS.js`
- **目录名**: kebab-case，如 `user-management/`

### 2.3 模块组织原则
- 按功能模块组织页面目录
- 页面私有组件放在对应页面的 `components/` 目录
- 页面共用逻辑放在对应页面的 `common/` 目录

## 3. 开发标准

### 3.1 页面组件开发规范

#### 3.1.1 基本结构
每个页面组件必须遵循以下结构：

```vue
<!-- [功能模块名称] -->
<template>
  <div>页面内容</div>
</template>

<script setup>
import { ref, computed, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'

// 响应式数据
const loading = ref(false)
const dataList = ref([])

// 计算属性
const pageTitle = computed(() => '页面标题')

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    // API 调用
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
onActivated(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: var(--van-background-2);
}

.content {
  padding: 16px;
}
</style>

<route lang="json5">
{
  name: 'PageName',
  meta: {
    title: '页面标题'
  }
}
</route>
```

#### 3.1.2 必需元素
1. app.vue已经包含导航栏，页面布局不需要包含导航和预留的padding-top导航栏间距
2. **路由块**: 必须包含 `<route>` 块定义路由信息
3. <style lang="scss" scoped> 必须添加 `scoped` 属性
4. app.vue 页面使用了<keep-alive>缓存，需要在页面实现缓存生命周期的刷新方法

### 3.2 代码风格规范

#### 3.2.1 Vue 组件规范
- 使用 Vue 3 Composition API
- 优先使用 `<script setup>` 语法
- 响应式数据使用 `ref()` 或 `reactive()`
- 计算属性使用 `computed()`
- 生命周期钩子按需导入

#### 3.2.2 变量和函数命名
- 响应式数据: camelCase，如 `userInfo`, `isLoading`
- 函数名: camelCase，动词开头，如 `fetchData`, `handleClick`
- 常量: UPPER_SNAKE_CASE，如 `API_BASE_URL`
- 组件 props: camelCase，如 `userName`, `isVisible`

#### 3.2.3 注释和文档标准
- API 函数必须包含 JSDoc 注释
- 复杂业务逻辑添加行内注释
- 组件 props 和 emits 添加类型注释

```javascript
/**
 * 获取待开会议列表
 * @param {Object} params - 查询参数
 * @param {number} params.current - 当前页码
 * @param {number} params.size - 每页条数
 * @param {string} params.assignee - 当前用户账号
 * @param {string} [params.conferenceName] - 会议名称（可选）
 * @returns {Promise} 返回会议列表数据
 */
```

## 4. UI/UX 指南

### 4.1 组件库使用
- **优先使用**: Vant UI 组件库（已全局注册）
- **组件前缀**: 所有 Vant 组件使用 `Van` 前缀，如 `VanButton`, `VanCell`
- **避免**: Element Plus 或其他 PC 端组件库

### 4.2 样式开发规范
- 优先使用 Vant 提供的 CSS 变量
- 颜色: `var(--van-primary-color)`, `var(--van-text-color)`
- 间距: `var(--van-padding-md)`, `var(--van-padding-lg)`
- 字体: `var(--van-font-size-md)`, `var(--van-font-size-lg)`
- 直接使用 px 单位，插件自动转换

### 4.3 响应式设计模式
- **设计基准**: 375px 宽度
- **最大显示宽度**: 600px
- **单位使用**: 直接使用 px，postcss-mobile-forever 自动转换
- **安全区域**: 使用 `env(safe-area-inset-bottom)` 适配

### 4.4 主题定制
在 `src/style/theme.css` 中修改 Vant 主题变量：比如主题色为绿色，直接css变量 --van-primary-color:#07c160。


## 5. 构建和配置

### 5.1 包管理
- **包管理器**: pnpm (必须使用)
- **依赖安装**: `pnpm install`
- **开发启动**: `pnpm dev`
- **生产构建**: `pnpm build`

### 5.2 构建配置
- **基础路径**: 相对路径 `./`
- **开发端口**: 8000
- **代理配置**: `/api` 路径代理到后端
- **别名配置**: `@` -> `src/`, `~` -> `src/assets/`

### 5.3 环境配置
- **Node.js**: >= 20.0.0
- **pnpm**: >= 7.0.0
- **开发工具**: 推荐 VS Code + Volar 扩展

## 6. 核心功能使用指南

### 6.1 文件系统路由

#### 6.1.1 路由映射规则
- `src/pages/home/<USER>/home`
- `src/pages/user/profile.vue` → `/user/profile`
- `src/pages/meeting/[id].vue` → `/meeting/:id` (动态路由)

#### 6.1.2 路由配置
```vue
<route lang="json5">
{
  name: 'MeetingDetail',
  meta: {
    title: '会议详情',
    requiresAuth: true,
    keepAlive: false
  }
}
</route>
```

#### 6.1.3 排除规则
- `components/` 文件夹自动排除
- `common/` 文件夹自动排除

### 6.2 状态管理 (Pinia)

#### 6.2.1 Store 创建模式
```javascript
// src/stores/modules/user.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const token = ref('')

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)

  // 方法
  const login = async (credentials) => {
    const response = await window.$http.post('/api/login', credentials)
    token.value = response.token
    userInfo.value = response.user
  }

  return {
    userInfo,
    token,
    isLoggedIn,
    login
  }
}, {
  persist: true // 启用持久化
})
```

#### 6.2.2 Store 使用模式
```vue
<script setup>
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()

// 访问状态
console.log(userStore.userInfo)

// 调用方法
const handleLogin = () => {
  userStore.login({ username: 'admin', password: '123456' })
}
</script>
```

### 6.3 HTTP 请求规范
详情参考文档：[http请求](docs/https.md)


### 6.4 组件开发规范

#### 6.4.1 公共组件
- 放在 `src/components/` 目录
- 使用 PascalCase 命名
- 自动全局注册（通过 unplugin-vue-components）

#### 6.4.2 页面私有组件
- 放在对应页面的 `components/` 目录
- 不会被路由系统识别
- 仅在当前页面模块内使用


## 8. 开发注意事项和最佳实践

### 8.1 强制性规范
1. **组件库优先级**: 必须优先使用 Vant UI 组件
3. **路由配置**: 每个页面必须包含 `<route>` 块
4. **样式作用域**: 页面样式必须添加 `scoped` 属性
5. **项目结构更新**: 文件结构变更后必须更新 `docs/项目结构.md`
6. 数据mock在api文件夹定义的接口里，不允许在页面上写mock数据