<!-- 新增采购项目页面 -->
<template>
  <div class="procurement-add-page">
    <van-form @submit="handleSubmit" ref="formRef">
      <van-cell-group inset title="基本信息">
        <van-field
          v-model="formData.name"
          name="name"
          label="项目名称"
          placeholder="请输入项目名称"
          :rules="[{ required: true, message: '请输入项目名称' }]"
        />
        
        <van-field
          v-model="categoryText"
          is-link
          readonly
          name="category"
          label="项目分类"
          placeholder="请选择项目分类"
          @click="showCategoryPicker = true"
          :rules="[{ required: true, message: '请选择项目分类' }]"
        />
        
        <van-field
          v-model="formData.procurementDate"
          is-link
          readonly
          name="procurementDate"
          label="采购日期"
          placeholder="请选择采购日期"
          @click="showDatePicker = true"
          :rules="[{ required: true, message: '请选择采购日期' }]"
        />
        
        <van-field
          v-model="formData.budget"
          name="budget"
          label="项目预算"
          placeholder="请输入项目预算(万元)"
          type="number"
          :rules="[
            { required: true, message: '请输入项目预算' },
            { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的金额' }
          ]"
        />
        
        <van-field
          v-model="formData.description"
          name="description"
          label="项目描述"
          type="textarea"
          placeholder="请输入项目描述"
          rows="3"
          autosize
        />
      </van-cell-group>

      <van-cell-group inset title="采购单位信息">
        <van-field
          v-model="formData.procuringEntity"
          name="procuringEntity"
          label="采购单位"
          placeholder="请输入采购单位名称"
          :rules="[{ required: true, message: '请输入采购单位名称' }]"
        />
        
        <van-field
          v-model="formData.contactPerson"
          name="contactPerson"
          label="联系人"
          placeholder="请输入联系人姓名"
          :rules="[{ required: true, message: '请输入联系人姓名' }]"
        />
        
        <van-field
          v-model="formData.contactPhone"
          name="contactPhone"
          label="联系电话"
          placeholder="请输入联系电话"
          :rules="[
            { required: true, message: '请输入联系电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]"
        />
        
        <van-field
          v-model="formData.entityAddress"
          name="entityAddress"
          label="单位地址"
          placeholder="请输入单位地址"
          :rules="[{ required: true, message: '请输入单位地址' }]"
        />
      </van-cell-group>
      
      <div class="submit-section">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="submitting"
        >
          {{ submitting ? '提交中...' : '保存' }}
        </van-button>
      </div>
    </van-form>

    <!-- 分类选择器 -->
    <van-popup v-model:show="showCategoryPicker" position="bottom">
      <van-picker
        :columns="categoryOptions"
        @confirm="onCategoryConfirm"
        @cancel="showCategoryPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import { procurementApi } from '@/api/procurement'

const router = useRouter()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const showCategoryPicker = ref(false)
const showDatePicker = ref(false)
const currentDate = ref(new Date())
const categoryOptions = ref([])

const formData = reactive({
  name: '',
  category: '',
  procurementDate: '',
  budget: '',
  description: '',
  procuringEntity: '',
  contactPerson: '',
  contactPhone: '',
  entityAddress: ''
})

const minDate = new Date()
const maxDate = new Date(new Date().getFullYear() + 2, 11, 31)

// 计算属性
const categoryText = computed(() => {
  const option = categoryOptions.value.find(item => item.value === formData.category)
  return option ? option.text : ''
})

// 方法
const fetchCategories = async () => {
  try {
    const response = await procurementApi.getCategories()
    categoryOptions.value = response.map(item => ({
      text: item.name,
      value: item.id
    }))
  } catch (error) {
    console.error('获取项目分类失败:', error)
    // 使用模拟数据
    categoryOptions.value = [
      { text: '测试无审请项目', value: 'test_no_approval' },
      { text: '工程建设项目', value: 'construction' },
      { text: '货物采购项目', value: 'goods' },
      { text: '服务采购项目', value: 'service' }
    ]
  }
}

const onCategoryConfirm = ({ selectedOptions }) => {
  formData.category = selectedOptions[0].value
  showCategoryPicker.value = false
}

const onDateConfirm = (value) => {
  const year = value.getFullYear()
  const month = String(value.getMonth() + 1).padStart(2, '0')
  const day = String(value.getDate()).padStart(2, '0')
  formData.procurementDate = `${year}-${month}-${day}`
  showDatePicker.value = false
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    const submitData = {
      name: formData.name.trim(),
      category: formData.category,
      procurementDate: formData.procurementDate,
      budget: parseFloat(formData.budget),
      description: formData.description.trim(),
      procuringEntity: formData.procuringEntity.trim(),
      contactPerson: formData.contactPerson.trim(),
      contactPhone: formData.contactPhone.trim(),
      entityAddress: formData.entityAddress.trim()
    }
    
    await procurementApi.createProject(submitData)
    
    showSuccessToast('采购项目创建成功')
    router.back()
    
  } catch (error) {
    console.error('创建采购项目失败:', error)
    showToast('创建失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchCategories()
})
</script>

<style lang="scss" scoped>
.procurement-add-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 100px;
  flex-shrink: 0;
}

:deep(.van-field__control) {
  text-align: right;
}
</style>

<route lang="json5">
{
  name: 'ProcurementAdd',
  meta: {
    title: '新增采购项目'
  }
}
</route>
