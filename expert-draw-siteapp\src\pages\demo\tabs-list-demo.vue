<!-- FuniList Tabs 功能示例 -->
<template>
  <div class="tabs-list-demo-page">
    <van-nav-bar title="Tabs列表示例" left-arrow @click-left="$router.back()" />
    
    <!-- 多 Tabs 列表示例 -->
    <FuniList
      ref="tabsListRef"
      :tabs="tabsConfig"
      search-placeholder="搜索内容"
      item-key="id"
      @item-click="handleItemClick"
      @tab-change="handleTabChange"
    >
      <!-- 自定义头部 -->
      <template #header>
        <div class="demo-header">
          <van-notice-bar text="这是多 Tabs 列表示例，支持不同类型的数据展示" />
        </div>
      </template>

      <!-- 自定义列表项 -->
      <template #item="{ item, index, tab }">
        <div class="custom-item" :class="`item-${tab?.key}`">
          <div class="item-header">
            <div class="item-title">{{ item.title || item.name }}</div>
            <div class="item-badge">
              <van-tag :type="getItemType(tab?.key)">{{ tab?.title }}</van-tag>
            </div>
          </div>
          <div class="item-content">{{ item.description || item.content }}</div>
          <div class="item-meta">
            <span class="item-time">{{ formatTime(item.createTime) }}</span>
            <span class="item-status">{{ item.status }}</span>
          </div>
        </div>
      </template>

      <!-- 自定义底部 -->
      <template #footer>
        <div class="demo-footer">
          <van-button type="primary" size="small" @click="refreshCurrentTab">
            刷新当前标签
          </van-button>
          <van-button type="default" size="small" @click="switchToTab('news')">
            切换到新闻
          </van-button>
        </div>
      </template>
    </FuniList>

    <!-- 单 Tab 列表示例 -->
    <div class="single-tab-section">
      <h3>单 Tab 列表（不显示 Tabs 切换）</h3>
      <FuniList
        ref="singleListRef"
        :tabs="singleTabConfig"
        search-placeholder="搜索用户"
        item-key="id"
        @item-click="handleUserClick"
      >
        <template #item="{ item }">
          <div class="user-item">
            <div class="user-avatar">
              <van-icon name="contact" />
            </div>
            <div class="user-info">
              <div class="user-name">{{ item.name }}</div>
              <div class="user-email">{{ item.email }}</div>
            </div>
          </div>
        </template>
      </FuniList>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import FuniList from '@/components/FuniList.vue'
import moment from 'moment'

const router = useRouter()

const tabsListRef = ref()
const singleListRef = ref()

// 多 Tabs 配置
const tabsConfig = [
  {
    key: 'news',
    title: '新闻',
    badge: '5',
    loadFunction: loadNewsData,
    filterConfig: [
      {
        type: 'select',
        label: '分类',
        prop: 'category',
        options: [
          { text: '全部', value: '' },
          { text: '科技', value: 'tech' },
          { text: '体育', value: 'sports' },
          { text: '娱乐', value: 'entertainment' }
        ],
        props: {
          placeholder: '请选择分类'
        }
      }
    ],
    extraParams: { type: 'news' }
  },
  {
    key: 'articles',
    title: '文章',
    loadFunction: loadArticlesData,
    filterConfig: [
      {
        type: 'input',
        label: '作者',
        prop: 'author',
        props: {
          placeholder: '请输入作者姓名',
          clearable: true
        }
      },
      {
        type: 'date',
        label: '发布日期',
        prop: 'publishDate',
        props: {
          placeholder: '请选择发布日期',
          dateType: 'date'
        }
      }
    ],
    extraParams: { type: 'article' }
  },
  {
    key: 'videos',
    title: '视频',
    dot: true,
    loadFunction: loadVideosData,
    filterConfig: [
      {
        type: 'select',
        label: '时长',
        prop: 'duration',
        options: [
          { text: '全部', value: '' },
          { text: '短视频(< 5分钟)', value: 'short' },
          { text: '中等(5-30分钟)', value: 'medium' },
          { text: '长视频(> 30分钟)', value: 'long' }
        ],
        props: {
          placeholder: '请选择时长'
        }
      }
    ],
    extraParams: { type: 'video' }
  }
]

// 单 Tab 配置（不显示 Tabs 切换）
const singleTabConfig = [
  {
    key: 'users',
    title: '用户列表',
    loadFunction: loadUsersData,
    filterConfig: [
      {
        type: 'input',
        label: '姓名',
        prop: 'name',
        props: {
          placeholder: '请输入用户姓名',
          clearable: true
        }
      },
      {
        type: 'select',
        label: '状态',
        prop: 'status',
        options: [
          { text: '全部', value: '' },
          { text: '活跃', value: 'active' },
          { text: '非活跃', value: 'inactive' }
        ],
        props: {
          placeholder: '请选择状态'
        }
      }
    ]
  }
]

// 模拟数据加载函数
async function loadNewsData(params) {
  console.log('加载新闻数据:', params)
  
  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  const mockData = Array.from({ length: params.pageSize }, (_, index) => ({
    id: `news_${params.page}_${index}`,
    title: `新闻标题 ${params.page}-${index + 1}`,
    description: '这是一条新闻的描述内容，包含了新闻的主要信息...',
    category: ['tech', 'sports', 'entertainment'][index % 3],
    createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    status: '已发布'
  }))
  
  return {
    data: mockData,
    total: 100
  }
}

async function loadArticlesData(params) {
  console.log('加载文章数据:', params)
  
  await new Promise(resolve => setTimeout(resolve, 800))
  
  const mockData = Array.from({ length: params.pageSize }, (_, index) => ({
    id: `article_${params.page}_${index}`,
    title: `文章标题 ${params.page}-${index + 1}`,
    description: '这是一篇文章的摘要内容，介绍了文章的主要观点和内容...',
    author: `作者${index + 1}`,
    createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    status: '已发布'
  }))
  
  return {
    data: mockData,
    total: 80
  }
}

async function loadVideosData(params) {
  console.log('加载视频数据:', params)
  
  await new Promise(resolve => setTimeout(resolve, 1200))
  
  const mockData = Array.from({ length: params.pageSize }, (_, index) => ({
    id: `video_${params.page}_${index}`,
    title: `视频标题 ${params.page}-${index + 1}`,
    description: '这是一个视频的描述，包含了视频的主要内容和亮点...',
    duration: ['short', 'medium', 'long'][index % 3],
    createTime: new Date(Date.now() - Math.random() * 14 * 24 * 60 * 60 * 1000),
    status: '已上线'
  }))
  
  return {
    data: mockData,
    total: 60
  }
}

async function loadUsersData(params) {
  console.log('加载用户数据:', params)
  
  await new Promise(resolve => setTimeout(resolve, 600))
  
  const mockData = Array.from({ length: params.pageSize }, (_, index) => ({
    id: `user_${params.page}_${index}`,
    name: `用户${params.page}-${index + 1}`,
    email: `user${params.page}${index}@example.com`,
    status: index % 2 === 0 ? 'active' : 'inactive',
    createTime: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000)
  }))
  
  return {
    data: mockData,
    total: 50
  }
}

// 方法
const getItemType = (tabKey) => {
  const typeMap = {
    'news': 'primary',
    'articles': 'success',
    'videos': 'warning'
  }
  return typeMap[tabKey] || 'default'
}

const formatTime = (time) => {
  return moment(time).format('MM-DD HH:mm')
}

const handleItemClick = (item, index) => {
  showToast(`点击了: ${item.title || item.name}`)
}

const handleUserClick = (user, index) => {
  showToast(`点击了用户: ${user.name}`)
}

const handleTabChange = (tabKey, tab) => {
  console.log('Tab 切换:', tabKey, tab)
  showToast(`切换到: ${tab.title}`)
}

const refreshCurrentTab = () => {
  if (tabsListRef.value) {
    tabsListRef.value.refresh()
    showToast('当前标签已刷新')
  }
}

const switchToTab = (tabKey) => {
  if (tabsListRef.value) {
    tabsListRef.value.switchTab(tabKey)
  }
}
</script>

<style lang="scss" scoped>
.tabs-list-demo-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.demo-header {
  padding: 8px 16px;
  background: white;
}

.demo-footer {
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
}

.custom-item {
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .item-title {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
    }
  }

  .item-content {
    font-size: 14px;
    color: #646566;
    line-height: 1.5;
    margin-bottom: 8px;
  }

  .item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #969799;
  }

  &.item-news {
    border-left: 3px solid #1989fa;
  }

  &.item-articles {
    border-left: 3px solid #07c160;
  }

  &.item-videos {
    border-left: 3px solid #ff976a;
  }
}

.single-tab-section {
  margin: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  height: 300px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #323233;
  }
}

.user-item {
  display: flex;
  align-items: center;
  gap: 12px;

  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;

    .van-icon {
      font-size: 20px;
      color: #646566;
    }
  }

  .user-info {
    flex: 1;

    .user-name {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .user-email {
      font-size: 12px;
      color: #646566;
    }
  }
}
</style>

<route lang="json5">
{
  name: 'TabsListDemo',
  meta: {
    title: 'Tabs列表示例'
  }
}
</route>
