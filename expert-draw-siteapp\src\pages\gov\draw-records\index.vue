<!-- 名单管理列表页面 -->
<template>
  <div class="draw-records-page">
    <!-- 列表内容 -->
    <div class="records-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="record in recordsList"
            :key="record.id"
            class="record-card"
            @click="handleRecordDetail(record)"
          >
            <!-- 状态标签 -->
            <div class="status-tags">
              <van-tag 
                v-for="tag in getStatusTags(record)" 
                :key="tag.text"
                :type="tag.type" 
                size="small"
                class="status-tag"
              >
                {{ tag.text }}
              </van-tag>
            </div>

            <!-- 项目信息 -->
            <div class="project-info">
              <div class="project-header">
                <span class="project-code">项目编号：{{ record.code }}</span>
                <span class="project-status" :class="getFlowStatusClass(record.flowStatus)">
                  {{ getFlowStatusText(record.flowStatus) }}
                </span>
              </div>
              <div class="project-name">{{ record.name }}</div>
            </div>

            <!-- 详细信息 -->
            <div class="detail-info">
              <div class="info-row">
                <div class="info-item">
                  <span class="info-label">项目类型</span>
                  <span class="info-value">{{ getProjectTypeText(record.type) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">经办人</span>
                  <span class="info-value">{{ record.createUser?.nickname || '未知' }}</span>
                </div>
              </div>
              
              <div class="info-row">
                <div class="info-item">
                  <span class="info-label">抽取专家</span>
                  <span class="info-value">{{ record.expertName || '未抽取' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">经办处室</span>
                  <span class="info-value">{{ getDepartmentName(record.createUser) }}</span>
                </div>
              </div>
              
              <div class="info-row">
                <div class="info-item">
                  <span class="info-label">经办处室</span>
                  <span class="info-value">{{ getDepartmentName(record.createUser) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">经办时间</span>
                  <span class="info-value">{{ formatDateTime(record.createTime) }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
              <van-button 
                type="default" 
                size="small" 
                @click.stop="handleDetail(record)"
              >
                详情
              </van-button>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && recordsList.length === 0"
      description="暂无抽取记录"
      image="search"
    />

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAddRecord"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { drawApi } from '@/api/draw'
import { useFlowStatus } from '@/composables/useEnums'

const router = useRouter()

// 使用枚举数据
const { getDesc: getFlowStatusDesc, getColor: getFlowStatusColor } = useFlowStatus()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const recordsList = ref([])
const currentPage = ref(1)
const pageSize = ref(20)

// 方法
const fetchRecordsList = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1
      finished.value = false
    }
    
    loading.value = true
    const params = {
      page: currentPage.value.toString(),
      pageSize: pageSize.value.toString(),
      timestamp: Date.now().toString()
    }
    
    const response = await drawApi.getDrawRecords(params)
    
    if (isRefresh) {
      recordsList.value = response || []
    } else {
      recordsList.value.push(...(response || []))
    }
    
    // 判断是否还有更多数据
    if (!response || response.length < pageSize.value) {
      finished.value = true
    }
    
  } catch (error) {
    console.error('获取抽取记录失败:', error)
    showToast('获取数据失败')
    
    // 使用模拟数据
    if (isRefresh || recordsList.value.length === 0) {
      recordsList.value = [
        {
          id: '1',
          entityId: '1',
          applyStatus: 'APPROVED',
          name: '2024年市级供销合作社培育壮大工程专项资金',
          code: '88839003003',
          type: 'REVIEW',
          initFlag: true,
          flowStatus: 'APPROVAL',
          flowUser: {
            id: '1',
            nickname: '张三',
            post: '科员'
          },
          flowTime: '2025-12-12 12:22:22',
          history: false,
          drawStatus: null,
          drewTime: null,
          department: '成都市农业农村局',
          applyUserName: '李四',
          applyUserPhone: '138****5678',
          expertName: '张三',
          expertType: null,
          agencyName: null,
          funds: null,
          createUser: {
            id: '1',
            nickname: '张三',
            post: '科员',
            groups: [{
              name: '成都市农业农村局'
            }]
          },
          createTime: '2025-12-12 12:22:22'
        },
        {
          id: '2',
          entityId: '2',
          applyStatus: 'PENDING',
          name: '2024年市级供销合作社培育壮大工程专项资金',
          code: '88839003003',
          type: 'REVIEW',
          initFlag: false,
          flowStatus: 'FINISH',
          flowUser: {
            id: '2',
            nickname: '李四',
            post: '主任科员'
          },
          flowTime: '2025-12-12 12:22:22',
          history: false,
          drawStatus: null,
          drewTime: null,
          department: '成都市农业农村局',
          applyUserName: '王五',
          applyUserPhone: '139****1234',
          expertName: '张三',
          expertType: null,
          agencyName: null,
          funds: null,
          createUser: {
            id: '2',
            nickname: '张三',
            post: '主任科员',
            groups: [{
              name: '成都市农业农村局'
            }]
          },
          createTime: '2025-12-12 12:22:22'
        },
        {
          id: '3',
          entityId: '3',
          applyStatus: 'REJECTED',
          name: '2024年市级供销合作社培育壮大工程专项资金',
          code: '88839003003',
          type: 'REVIEW',
          initFlag: true,
          flowStatus: 'REJECT',
          flowUser: {
            id: '3',
            nickname: '王五',
            post: '副主任'
          },
          flowTime: '2025-12-12 12:22:22',
          history: false,
          drawStatus: null,
          drewTime: null,
          department: '成都市农业农村局',
          applyUserName: '赵六',
          applyUserPhone: '137****9876',
          expertName: '张三',
          expertType: null,
          agencyName: null,
          funds: null,
          createUser: {
            id: '3',
            nickname: '张三',
            post: '副主任',
            groups: [{
              name: '成都市农业农村局'
            }]
          },
          createTime: '2025-12-12 12:22:22'
        },
        {
          id: '4',
          entityId: '4',
          applyStatus: 'APPROVED',
          name: '2024年市级供销合作社培育壮大工程专项资金',
          code: '88839003003',
          type: 'REVIEW',
          initFlag: false,
          flowStatus: 'FINISH',
          flowUser: {
            id: '4',
            nickname: '钱七',
            post: '处长'
          },
          flowTime: '2025-12-12 12:22:22',
          history: false,
          drawStatus: null,
          drewTime: null,
          department: '成都市农业农村局',
          applyUserName: '孙八',
          applyUserPhone: '135****2468',
          expertName: '张三',
          expertType: null,
          agencyName: null,
          funds: null,
          createUser: {
            id: '4',
            nickname: '张三',
            post: '处长',
            groups: [{
              name: '成都市农业农村局'
            }]
          },
          createTime: '2025-12-12 12:22:22'
        }
      ]
      finished.value = true
    }
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onLoad = () => {
  if (!finished.value) {
    currentPage.value++
    fetchRecordsList()
  }
}

const onRefresh = () => {
  fetchRecordsList(true)
}

const handleRecordDetail = (record) => {
  router.push(`/gov/draw-records/detail/${record.id}`)
}

const handleDetail = (record) => {
  router.push(`/gov/draw-records/detail/${record.id}`)
}

const handleAddRecord = () => {
  router.push('/gov/draw-records/add')
}

const getStatusTags = (record) => {
  const tags = []
  
  // 根据不同状态添加标签
  if (record.initFlag) {
    tags.push({ text: '本级', type: 'primary' })
  }
  
  if (record.flowStatus === 'APPROVAL') {
    tags.push({ text: '审批中', type: 'warning' })
  } else if (record.flowStatus === 'FINISH') {
    tags.push({ text: '审批通过', type: 'success' })
  } else if (record.flowStatus === 'REJECT') {
    tags.push({ text: '已驳回', type: 'danger' })
  }
  
  return tags
}

const getFlowStatusText = (status) => {
  return getFlowStatusDesc(status)
}

const getFlowStatusClass = (status) => {
  const classMap = {
    'UN_SUBMIT': 'status-draft',
    'APPROVAL': 'status-pending',
    'REJECT': 'status-rejected',
    'REVOKE': 'status-revoked',
    'FINISH': 'status-approved',
    'REJECT_FINISH': 'status-rejected'
  }
  return classMap[status] || ''
}

const getProjectTypeText = (type) => {
  const typeMap = {
    'REVIEW': '抽取专家',
    'PURCHASE': '抽取代理机构',
    'EVADE': '专家规避',
    'REVOKE': '代理机构撤回'
  }
  return typeMap[type] || type
}

const getDepartmentName = (user) => {
  return user?.groups?.[0]?.name || '未知部门'
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  // 如果已经是格式化的字符串，直接返回
  if (dateString.includes('-') && dateString.includes(':')) {
    return dateString
  }
  // 否则格式化日期
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

// 生命周期
onMounted(() => {
  fetchRecordsList(true)
})

onActivated(() => {
  fetchRecordsList(true)
})
</script>

<style lang="scss" scoped>
.draw-records-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.records-list {
  padding: 8px 16px;
}

.record-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.status-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  
  .status-tag {
    font-size: 10px;
  }
}

.project-info {
  margin-bottom: 12px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.project-code {
  font-size: 12px;
  color: var(--van-text-color-2);
}

.project-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
  &.status-pending {
    background-color: #FFF3E0;
    color: #FF9800;
  }
  
  &.status-approved {
    background-color: #E8F5E8;
    color: #4CAF50;
  }
  
  &.status-rejected {
    background-color: #FFEBEE;
    color: #F44336;
  }
  
  &.status-draft {
    background-color: var(--van-gray-1);
    color: var(--van-text-color-2);
  }
  
  &.status-revoked {
    background-color: #F3E5F5;
    color: #9C27B0;
  }
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  line-height: 1.4;
}

.detail-info {
  margin-bottom: 12px;
  
  .info-row {
    display: flex;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;
    
    &:first-child {
      margin-right: 16px;
    }
  }

  .info-label {
    font-size: 12px;
    color: var(--van-text-color-2);
    margin-bottom: 2px;
    display: block;
  }

  .info-value {
    font-size: 14px;
    color: var(--van-text-color);
  }
}

.action-section {
  display: flex;
  justify-content: flex-end;
  padding-top: 8px;
  border-top: 1px solid var(--van-border-color);
}
</style>

<route lang="json5">
{
  name: 'DrawRecords',
  meta: {
    title: '名单管理'
  }
}
</route>
