<!-- 名单管理列表页面 -->
<template>
  <div class="draw-records-page">
    <FuniList
      ref="recordsListRef"
      :tabs="tabsConfig"
      item-key="id"
      @item-click="handleRecordClick"
    >
    <template #recordsHeader>
      <div class="status-box">
        <div class="status-item">
          <div class="dot" :style="{ backgroundColor: getFlowStatusColor('UN_SUBMIT') }"></div>
          <div>未提交:{{ statusCount.unSubmitNum }}</div>
        </div>
        <div class="status-item">
          <div class="dot" :style="{ backgroundColor: getFlowStatusColor('APPROVAL') }"></div>
          <div>审批中:{{ statusCount.approvalNum }}</div>
        </div>
        <div class="status-item">
          <div class="dot" :style="{ backgroundColor: getFlowStatusColor('REJECT') }"></div>
          <div>已驳回:{{ statusCount.rejectNum }}</div>
        </div>
        <div class="status-item">
          <div class="dot" :style="{ backgroundColor: getFlowStatusColor('FINISH') }"></div>
          <div>审批通过:{{ statusCount.finishNum }}</div>
        </div>
      </div>
    </template>
      <template #item="{ item: record }">
        <div class="record-card">
          <!-- 项目信息 -->
          <div class="project-info">
            <div class="project-header">
              <span class="project-code"
                >项目编号：{{ record.code || "--" }}</span
              >
              <span
                class="project-status"
                :style="getFlowStatusStyle(record.flowStatus)"
              >
                {{ getFlowStatusText(record.flowStatus) }}
              </span>
            </div>
            <div class="project-name">{{ record.name }}</div>
          </div>

          <!-- 详细信息 -->
          <div class="detail-info">
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">项目类型</span>
                <span class="info-value">{{
                  getProjectTypeText(record.type)
                }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">经办人</span>
                <span class="info-value">{{
                  record.createUser?.nickname || "未知"
                }}</span>
              </div>
            </div>

            <div class="info-row">
              <div class="info-item">
                <span class="info-label">经办处室</span>
                <span class="info-value">
                  {{ record.department }}
                </span>
              </div>
              <div class="info-item">
                <span class="info-label">经办时间</span>
                <span class="info-value">
                  {{ record.createTime }}
                </span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <!-- <div class="action-section">
            <van-button
              type="default"
              block=""
              size="small"
              @click.stop="handleDetail(record)"
            >
              详情
            </van-button>
          </div> -->
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import { drawApi } from "@/api/draw";
import FuniList from "@/components/FuniList.vue";

const router = useRouter();
const recordsListRef = ref();
const statusCount = ref({})

// 数据加载函数
const loadDrawRecordsData = async (params) => {
  const response = await drawApi.getDrawRecords(params);
  statusCount.value = response.extras
  // 处理返回数据
  return {
    data: response.data,
    total: response.total,
  };
};

// Tabs 配置
const tabsConfig = [
  {
    key: "records",
    title: "抽取记录",
    loadFunction: loadDrawRecordsData,
    searchPlaceholder: "搜索项目名称/编号",
    keyword: "keyword",
    filterConfig: [
      {
        type: "select",
        label: "流程状态",
        prop: "flowStatus",
        options: [
          { text: "全部", value: "" },
          { text: "未提交", value: "UN_SUBMIT" },
          { text: "审批中", value: "APPROVAL" },
          { text: "已通过", value: "FINISH" },
          { text: "已驳回", value: "REJECT" },
          { text: "已撤回", value: "REVOKE" },
        ],
        props: {
          placeholder: "请选择流程状态",
        },
      },
      {
        type: "select",
        label: "项目类型",
        prop: "type",
        options: [
          { text: "全部", value: "" },
          { text: "抽取专家", value: "REVIEW" },
          { text: "抽取代理机构", value: "PURCHASE" },
          { text: "专家规避", value: "EVADE" },
          { text: "代理机构撤回", value: "REVOKE" },
        ],
        props: {
          placeholder: "请选择项目类型",
        },
      },
    ],
  },
];

// 事件处理方法
const handleRecordClick = (record, index) => {
  console.log("点击抽取记录:", record, index);
  handleRecordDetail(record);
};

const handleRecordDetail = (record) => {
  router.push(`/gov/draw-records/detail/${record.id}`);
};

const handleDetail = (record) => {
  router.push(`/gov/draw-records/detail/${record.id}`);
};

const getFlowStatusText = (status) => {
  return window.$enums.getEnumText("FlowStatus", status);
};

const getFlowStatusColor = (status) => {
  return window.$enums.getEnumColor("FlowStatus", status);
};
const getFlowStatusStyle = (status) => {
  let color = getFlowStatusColor(status);
  return {
    color,
    backgroundColor: color + "30",
  };
};

const getProjectTypeText = (type) => {
  return window.$enums.getEnumText("ApplyType", type);
};

</script>

<style lang="scss" scoped>
.draw-records-page {
  height: calc(100vh - 46px);
  background-color: var(--van-background);
  .status-box{
    display: flex;
    align-items: center;
    font-size: 11px;
    padding: 4px 16px;
    background-color: #fff;
    .status-item{
      flex:1;
      display: flex;
      align-items: center;
      .dot{
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #000;
        margin-right: 4px;
      }
    }
  }
}

.record-card {
}

.status-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;

  .status-tag {
    font-size: 10px;
  }
}

.project-info {
  margin-bottom: 12px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.project-code {
  font-size: 12px;
  color: var(--van-text-color-2);
}

.project-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  line-height: 1.4;
}

.detail-info {
  margin-bottom: 12px;

  .info-row {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    flex: 1;

    &:first-child {
      margin-right: 16px;
    }
  }

  .info-label {
    font-size: 12px;
    color: var(--van-text-color-2);
    margin-bottom: 2px;
    display: block;
  }

  .info-value {
    font-size: 14px;
    color: var(--van-text-color);
  }
}

.action-section {
  display: flex;
  justify-content: flex-end;
  padding-top: 8px;
}
</style>

<route lang="json5">
{
  name: "DrawRecords",
  meta: {
    title: "名单管理",
  },
}
</route>
