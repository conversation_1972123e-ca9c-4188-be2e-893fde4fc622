<!-- 日期表单示例 - 展示 moment.js 集成 -->
<template>
  <div class="date-form-demo-page">
    <van-nav-bar title="日期表单示例" left-arrow @click-left="$router.back()" />
    
    <div class="demo-section">
      <h3>日期时间表单示例</h3>
      <FuniForm
        ref="formRef"
        v-model="formData"
        :form-config="formConfig"
        :submit-loading="submitLoading"
        submit-text="提交表单"
        @submit="handleSubmit"
        @failed="handleFormFailed"
      />
    </div>
    
    <div class="demo-section">
      <h3>表单数据预览</h3>
      <van-cell-group>
        <van-cell title="原始数据" :value="JSON.stringify(formData)" />
        <van-cell title="格式化数据" :value="JSON.stringify(formattedData)" />
      </van-cell-group>
      
      <div class="format-buttons">
        <van-button type="primary" size="small" @click="getFormattedData">
          获取格式化数据
        </van-button>
        <van-button type="default" size="small" @click="setTestData">
          设置测试数据
        </van-button>
        <van-button type="warning" size="small" @click="resetForm">
          重置表单
        </van-button>
      </div>
    </div>
    
    <div class="demo-section">
      <h3>日期格式化示例</h3>
      <van-cell-group>
        <van-cell title="当前时间" :value="currentTime" />
        <van-cell title="格式化为日期" :value="formatCurrentDate('YYYY-MM-DD')" />
        <van-cell title="格式化为日期时间" :value="formatCurrentDate('YYYY-MM-DD HH:mm:ss')" />
        <van-cell title="格式化为中文日期" :value="formatCurrentDate('YYYY年MM月DD日')" />
        <van-cell title="相对时间" :value="relativeTime" />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import FuniForm from '@/components/FuniForm.vue'
import moment from 'moment'

const router = useRouter()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)
const formData = reactive({})
const formattedData = ref({})

// 表单配置
const formConfig = [
  {
    type: 'date',
    label: '出生日期',
    prop: 'birthDate',
    props: {
      placeholder: '请选择出生日期',
      dateType: 'date',
      maxDate: new Date(),
      rules: [{ required: true, message: '请选择出生日期' }]
    }
  },
  {
    type: 'date',
    label: '入职时间',
    prop: 'joinDateTime',
    props: {
      placeholder: '请选择入职时间',
      dateType: 'datetime',
      rules: [{ required: true, message: '请选择入职时间' }]
    }
  },
  {
    type: 'date',
    label: '毕业年月',
    prop: 'graduateMonth',
    props: {
      placeholder: '请选择毕业年月',
      dateType: 'year-month'
    }
  },
  {
    type: 'time',
    label: '工作时间',
    prop: 'workTime',
    props: {
      placeholder: '请选择工作时间',
      timeFormat: 'HH:mm'
    }
  },
  {
    type: 'time',
    label: '下班时间',
    prop: 'offWorkTime',
    props: {
      placeholder: '请选择下班时间',
      timeFormat: 'HH:mm:ss'
    }
  },
  {
    type: 'input',
    label: '姓名',
    prop: 'name',
    props: {
      placeholder: '请输入姓名',
      rules: [{ required: true, message: '请输入姓名' }]
    }
  },
  {
    type: 'textarea',
    label: '备注',
    prop: 'remark',
    props: {
      placeholder: '请输入备注信息',
      rows: 3
    }
  }
]

// 计算属性
const currentTime = computed(() => {
  return moment().format('YYYY-MM-DD HH:mm:ss')
})

const relativeTime = computed(() => {
  return moment().fromNow()
})

// 方法
const formatCurrentDate = (format) => {
  return moment().format(format)
}

const getFormattedData = () => {
  if (formRef.value) {
    formattedData.value = formRef.value.getValues()
    showSuccessToast('已获取格式化数据')
  }
}

const setTestData = () => {
  const testData = {
    birthDate: '1990-05-15',
    joinDateTime: '2020-03-01 09:00:00',
    graduateMonth: '2015-06',
    workTime: '09:00',
    offWorkTime: '18:00:00',
    name: '张三',
    remark: '这是一个测试数据示例'
  }
  
  if (formRef.value) {
    formRef.value.setValues(testData)
    showSuccessToast('已设置测试数据')
  }
}

const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  formattedData.value = {}
  
  if (formRef.value) {
    formRef.value.resetValidation()
  }
  
  showSuccessToast('表单已重置')
}

const handleSubmit = async (values) => {
  try {
    submitLoading.value = true
    console.log('表单提交数据:', values)
    
    // 展示提交的数据
    formattedData.value = values
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showSuccessToast('表单提交成功')
  } catch (error) {
    console.error('表单提交失败:', error)
    showToast('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

const handleFormFailed = (errorInfo) => {
  console.log('表单验证失败:', errorInfo)
  showToast('请检查表单填写')
}
</script>

<style lang="scss" scoped>
.date-form-demo-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.demo-section {
  margin: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--van-text-color);
  }
}

.format-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  flex-wrap: wrap;
}

:deep(.van-cell__value) {
  word-break: break-all;
  font-size: 12px;
}
</style>

<route lang="json5">
{
  name: 'DateFormDemo',
  meta: {
    title: '日期表单示例'
  }
}
</route>
