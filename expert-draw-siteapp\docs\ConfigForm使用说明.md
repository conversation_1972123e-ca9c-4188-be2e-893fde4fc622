# FuniForm 配置化表单组件使用说明

## 概述

FuniForm 是一个基于 Vant 的配置化表单组件，支持通过配置的方式快速创建各种类型的表单，减少重复代码，提高开发效率。组件集成了 moment.js 来处理日期时间，提供更强大和灵活的日期时间处理能力。

## 特性

- 🚀 **配置化**: 通过配置数组快速生成表单
- 📱 **移动端优化**: 基于 Vant 组件库，完美适配移动端
- 🎯 **类型丰富**: 支持 12+ 种表单控件类型
- ✅ **验证支持**: 内置常用验证规则，支持自定义验证
- 🔧 **灵活扩展**: 支持自定义配置和样式
- 📦 **开箱即用**: 提供工具函数快速生成配置
- 📅 **日期处理**: 集成 moment.js，提供强大的日期时间处理能力
- 🌍 **国际化**: 支持中文本地化的日期时间显示

## 支持的表单控件类型

| 类型 | 说明 | 对应组件 |
|------|------|----------|
| input | 输入框 | van-field |
| textarea | 文本域 | van-field |
| select | 选择器 | van-field + van-picker |
| date | 日期选择器 | van-field + van-date-picker |
| time | 时间选择器 | van-field + van-time-picker |
| number | 数字输入框 | van-field |
| switch | 开关 | van-switch |
| radio | 单选框组 | van-radio-group |
| checkbox | 复选框组 | van-checkbox-group |
| rate | 评分 | van-rate |
| slider | 滑块 | van-slider |
| stepper | 步进器 | van-stepper |
| upload | 上传 | van-uploader |

## 基本使用

### 1. 导入组件

```vue
<script setup>
import FuniForm from '@/components/FuniForm.vue'
</script>
```

### 2. 定义表单配置

```javascript
const formConfig = [
  {
    type: 'input',
    label: '姓名',
    prop: 'name',
    props: {
      placeholder: '请输入姓名',
      rules: [{ required: true, message: '请输入姓名' }]
    }
  },
  {
    type: 'select',
    label: '性别',
    prop: 'gender',
    options: [
      { text: '男', value: 'male' },
      { text: '女', value: 'female' }
    ],
    props: {
      placeholder: '请选择性别',
      rules: [{ required: true, message: '请选择性别' }]
    }
  }
]
```

### 3. 使用组件

```vue
<template>
  <FuniForm
    v-model="formData"
    :form-config="formConfig"
    :submit-loading="submitLoading"
    @submit="handleSubmit"
    @failed="handleFormFailed"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = reactive({})
const submitLoading = ref(false)

const handleSubmit = (values) => {
  console.log('表单数据:', values)
}

const handleFormFailed = (errorInfo) => {
  console.log('验证失败:', errorInfo)
}
</script>
```

## 配置结构说明

每个字段配置包含以下基本结构：

```javascript
{
  type: '组件类型',        // 必填：表单控件类型
  label: '字段标签',       // 必填：字段显示标签
  prop: '字段属性名',      // 必填：对应表单数据的属性名
  options: [],           // 可选：选择器类型字段的选项数组
  props: {               // 可选：组件的具体属性配置
    placeholder: '占位符',
    rules: [],           // 验证规则
    disabled: false,     // 是否禁用
    // ... 其他组件特定属性
  }
}
```

### 配置示例

```javascript
const formConfig = [
  // 输入框
  {
    type: 'input',
    label: '姓名',
    prop: 'name',
    props: {
      placeholder: '请输入姓名',
      rules: [{ required: true, message: '请输入姓名' }],
      maxlength: 20,
      showWordLimit: true
    }
  },

  // 选择器
  {
    type: 'select',
    label: '类型',
    prop: 'type',
    options: [
      { text: '类型1', value: 'type1' },
      { text: '类型2', value: 'type2' }
    ],
    props: {
      placeholder: '请选择类型',
      rules: [{ required: true, message: '请选择类型' }]
    }
  },

  // 日期选择器
  {
    type: 'date',
    label: '日期',
    prop: 'date',
    props: {
      placeholder: '请选择日期',
      dateType: 'date',
      rules: [{ required: true, message: '请选择日期' }]
    }
  }
]
```

## 字段配置详解

### 通用配置

所有字段类型都支持以下通用配置：

```javascript
{
  type: 'input',           // 字段类型
  label: '字段标签',        // 字段标签
  prop: 'fieldName',       // 字段属性名
  props: {                 // 组件属性配置
    placeholder: '占位符',  // 占位符文本
    rules: [],            // 验证规则
    defaultValue: '',     // 默认值
    disabled: false,      // 是否禁用
    readonly: false       // 是否只读
  }
}
```

### 输入框 (input)

```javascript
{
  type: 'input',
  label: '用户名',
  prop: 'username',
  props: {
    placeholder: '请输入用户名',
    type: 'text',         // 输入类型: text, tel, email, password 等
    maxlength: 20,        // 最大长度
    showWordLimit: true,  // 显示字数统计
    clearable: true       // 显示清除按钮
  }
}
```

### 选择器 (select)

```javascript
{
  type: 'select',
  label: '城市',
  prop: 'city',
  options: [              // 选项数组
    { text: '北京', value: 'beijing' },
    { text: '上海', value: 'shanghai' }
  ],
  props: {
    placeholder: '请选择城市'
  }
}
```

### 日期选择器 (date)

```javascript
{
  type: 'date',
  label: '生日',
  prop: 'birthday',
  props: {
    placeholder: '请选择生日',
    dateType: 'date',       // 日期类型: date, datetime, year-month
    minDate: new Date(1900, 0, 1),  // 最小日期
    maxDate: new Date()     // 最大日期
  }
}
```

### 单选框组 (radio)

```javascript
{
  type: 'radio',
  label: '性别',
  prop: 'gender',
  options: [
    { text: '男', value: 'male' },
    { text: '女', value: 'female' }
  ],
  props: {
    direction: 'horizontal' // 排列方向: horizontal, vertical
  }
}
```

## 常用验证规则

```javascript
import { commonRules } from '@/utils/formConfig'

// 必填
commonRules.required('请输入姓名')

// 手机号
commonRules.phone

// 邮箱
commonRules.email

// 身份证号
commonRules.idCard

// 数字
commonRules.number

// 小数
commonRules.decimal

// 最小长度
commonRules.minLength(6)

// 最大长度
commonRules.maxLength(20)
```

## 组件 Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| formConfig | 表单配置数组 | Array | [] |
| modelValue | 表单数据 | Object | {} |
| showSubmitButton | 是否显示提交按钮 | Boolean | true |
| submitText | 提交按钮文本 | String | '提交' |
| submitLoading | 提交按钮加载状态 | Boolean | false |
| submitDisabled | 提交按钮禁用状态 | Boolean | false |

## 组件 Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 表单数据更新 | formData |
| submit | 表单提交 | formData |
| failed | 表单验证失败 | errorInfo |

## 组件方法

通过 ref 可以调用以下方法：

```javascript
const formRef = ref()

// 验证表单
formRef.value.validate()

// 重置验证
formRef.value.resetValidation()

// 获取表单数据
const values = formRef.value.getValues()

// 设置表单数据
formRef.value.setValues({ name: '张三', age: 25 })
```

## 完整示例

```vue
<template>
  <ConfigForm
    ref="formRef"
    v-model="formData"
    :form-config="formConfig"
    :submit-loading="submitLoading"
    submit-text="保存"
    @submit="handleSubmit"
    @failed="handleFormFailed"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'
import FuniForm from '@/components/FuniForm.vue'
import { createInputField, createSelectField, commonRules } from '@/utils/formConfig'

const formRef = ref()
const formData = reactive({})
const submitLoading = ref(false)

const formConfig = [
  createInputField({
    name: 'name',
    label: '姓名',
    rules: [commonRules.required('请输入姓名')],
    maxlength: 20,
    showWordLimit: true
  }),
  createSelectField({
    name: 'gender',
    label: '性别',
    options: [
      { text: '男', value: 'male' },
      { text: '女', value: 'female' }
    ],
    rules: [commonRules.required('请选择性别')]
  })
]

const handleSubmit = async (values) => {
  try {
    submitLoading.value = true
    // 处理提交逻辑
    console.log('提交数据:', values)
  } finally {
    submitLoading.value = false
  }
}

const handleFormFailed = (errorInfo) => {
  console.log('验证失败:', errorInfo)
}
</script>
```

## 日期时间处理 (Moment.js 集成)

FuniForm 组件集成了 moment.js 来处理日期时间，提供更强大和灵活的日期时间处理能力。

### 支持的日期类型

| 类型 | 格式 | 说明 |
|------|------|------|
| date | YYYY-MM-DD | 标准日期格式 |
| datetime | YYYY-MM-DD HH:mm:ss | 日期时间格式 |
| year-month | YYYY-MM | 年月格式 |
| year | YYYY | 年份格式 |
| month-day | MM-DD | 月日格式 |
| time | HH:mm:ss | 时间格式 |

### 日期字段配置

```javascript
{
  type: 'date',
  label: '出生日期',
  prop: 'birthDate',
  props: {
    placeholder: '请选择出生日期',
    dateType: 'date',           // 日期类型
    minDate: new Date(1900, 0, 1), // 最小日期
    maxDate: new Date(),        // 最大日期
    rules: [{ required: true, message: '请选择出生日期' }]
  }
}
```

### 时间字段配置

```javascript
{
  type: 'time',
  label: '工作时间',
  prop: 'workTime',
  props: {
    placeholder: '请选择工作时间',
    timeFormat: 'HH:mm',       // 时间格式：HH:mm 或 HH:mm:ss
    rules: [{ required: true, message: '请选择工作时间' }]
  }
}
```

### 日期时间格式化

组件会自动根据配置的 `dateType` 和 `timeFormat` 来格式化日期时间数据：

```javascript
// 表单提交时，日期会自动格式化
const handleSubmit = (values) => {
  console.log(values)
  // {
  //   birthDate: '1990-05-15',           // date 类型
  //   joinDateTime: '2020-03-01 09:00:00', // datetime 类型
  //   graduateMonth: '2015-06',          // year-month 类型
  //   workTime: '09:00'                  // time 类型
  // }
}
```

### 组件方法

FuniForm 提供了额外的日期处理方法：

```javascript
const formRef = ref()

// 获取格式化后的表单数据
const formattedData = formRef.value.getValues()

// 获取原始表单数据（不格式化）
const rawData = formRef.value.getRawValues()

// 格式化指定日期
const formattedDate = formRef.value.formatDate('2023-12-25', 'YYYY年MM月DD日')

// 解析日期字符串为 Date 对象
const dateObj = formRef.value.parseDate('2023-12-25')
```

### 中文本地化

组件已自动配置中文本地化，支持中文的日期时间显示：

```javascript
import moment from 'moment'

// 相对时间显示为中文
moment().fromNow() // "几秒前"、"3分钟前"、"2小时前" 等

// 中文日期格式
moment().format('YYYY年MM月DD日') // "2023年12月25日"
moment().format('dddd') // "星期一"
```

### 完整示例

```vue
<template>
  <FuniForm
    ref="formRef"
    v-model="formData"
    :form-config="formConfig"
    @submit="handleSubmit"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'
import FuniForm from '@/components/FuniForm.vue'

const formRef = ref()
const formData = reactive({})

const formConfig = [
  {
    type: 'date',
    label: '出生日期',
    prop: 'birthDate',
    props: {
      dateType: 'date',
      maxDate: new Date(),
      rules: [{ required: true, message: '请选择出生日期' }]
    }
  },
  {
    type: 'date',
    label: '入职时间',
    prop: 'joinDateTime',
    props: {
      dateType: 'datetime',
      rules: [{ required: true, message: '请选择入职时间' }]
    }
  },
  {
    type: 'time',
    label: '工作时间',
    prop: 'workTime',
    props: {
      timeFormat: 'HH:mm'
    }
  }
]

const handleSubmit = (values) => {
  console.log('格式化后的数据:', values)
  // {
  //   birthDate: '1990-05-15',
  //   joinDateTime: '2020-03-01 09:00:00',
  //   workTime: '09:00'
  // }
}
</script>
```

## 注意事项

1. 表单数据使用 `reactive` 创建，确保响应式更新
2. 选择器类型的字段需要提供 `options` 配置
3. 日期和时间选择器使用 moment.js 自动格式化数据
4. 上传组件需要配置 `maxSize` 和 `accept` 属性
5. 验证规则遵循 Vant Form 组件的规则格式
6. 日期时间字段支持多种格式，通过 `dateType` 和 `timeFormat` 配置
7. 组件已配置中文本地化，支持中文日期时间显示
