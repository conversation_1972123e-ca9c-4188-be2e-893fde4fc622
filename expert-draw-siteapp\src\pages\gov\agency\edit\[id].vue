<!-- 编辑代理机构页面 -->
<template>
  <div class="agency-edit-page">
    <van-loading v-if="loading" class="loading-center" size="24px">
      加载中...
    </van-loading>
    
    <van-form v-else @submit="handleSubmit" ref="formRef">
      <van-cell-group inset>
        <van-field
          v-model="formData.name"
          name="name"
          label="机构名称"
          placeholder="请输入机构名称"
          :rules="[{ required: true, message: '请输入机构名称' }]"
        />
        
        <van-field
          v-model="formData.leader"
          name="leader"
          label="负责人"
          placeholder="请输入负责人姓名"
          :rules="[{ required: true, message: '请输入负责人姓名' }]"
        />
        
        <van-field
          v-model="formData.leaderPhone"
          name="leaderPhone"
          label="负责人电话"
          placeholder="请输入负责人电话"
          :rules="[
            { required: true, message: '请输入负责人电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]"
        />
        
        <van-field
          v-model="formData.contact"
          name="contact"
          label="经办人1"
          placeholder="请输入经办人1姓名"
          :rules="[{ required: true, message: '请输入经办人1姓名' }]"
        />
        
        <van-field
          v-model="formData.phone"
          name="phone"
          label="经办人1电话"
          placeholder="请输入经办人1电话"
          :rules="[
            { required: true, message: '请输入经办人1电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]"
        />
        
        <van-field
          v-model="formData.contactb"
          name="contactb"
          label="经办人2"
          placeholder="请输入经办人2姓名（可选）"
        />
        
        <van-field
          v-model="formData.phoneb"
          name="phoneb"
          label="经办人2电话"
          placeholder="请输入经办人2电话（可选）"
          :rules="[
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'onBlur' }
          ]"
        />
        
        <van-field
          v-model="serviceRangeText"
          is-link
          readonly
          name="serviceRange"
          label="服务范围"
          placeholder="请选择服务范围"
          @click="showServiceRangePicker = true"
          :rules="[{ required: true, message: '请选择服务范围' }]"
        />
        
        <van-field name="enabled" label="状态">
          <template #input>
            <van-switch v-model="formData.enabled" />
          </template>
        </van-field>
      </van-cell-group>
      
      <div class="submit-section">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="submitting"
        >
          {{ submitting ? '保存中...' : '保存' }}
        </van-button>
      </div>
    </van-form>

    <!-- 服务范围选择器 -->
    <van-popup v-model:show="showServiceRangePicker" position="bottom">
      <van-picker
        :columns="serviceRangeOptions"
        @confirm="onServiceRangeConfirm"
        @cancel="showServiceRangePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import { agencyApi } from '@/api/agency'

const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const showServiceRangePicker = ref(false)

const formData = reactive({
  name: '',
  leader: '',
  leaderPhone: '',
  contact: '',
  phone: '',
  contactb: '',
  phoneb: '',
  serviceRange: '',
  enabled: true
})

// 服务范围选项
const serviceRangeOptions = [
  { text: '全部', value: 'ALL' },
  { text: '局机关和直属单位', value: 'DIRECTLY' },
  { text: '区县', value: 'DISTRICT' }
]

// 计算属性
const serviceRangeText = computed(() => {
  const option = serviceRangeOptions.find(item => item.value === formData.serviceRange)
  return option ? option.text : ''
})

// 方法
const fetchAgencyDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    const response = await agencyApi.getAgencyDetail(id)
    
    // 填充表单数据
    Object.assign(formData, {
      name: response.name || '',
      leader: response.leader || '',
      leaderPhone: response.leaderPhone || '',
      contact: response.contact || '',
      phone: response.phone || '',
      contactb: response.contactb || '',
      phoneb: response.phoneb || '',
      serviceRange: response.serviceRange || '',
      enabled: response.enabled !== false
    })
    
  } catch (error) {
    console.error('获取代理机构详情失败:', error)
    showToast('获取详情失败')
    
    // 使用模拟数据
    Object.assign(formData, {
      name: '四川鲁通天和招标代理有限公司',
      leader: '张三',
      leaderPhone: '18712345382',
      contact: '张三',
      phone: '18712345382',
      contactb: '李四',
      phoneb: '18712345382',
      serviceRange: 'DIRECTLY',
      enabled: true
    })
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      name: formData.name.trim(),
      leader: formData.leader.trim(),
      leaderPhone: formData.leaderPhone.trim(),
      contact: formData.contact.trim(),
      phone: formData.phone.trim(),
      contactb: formData.contactb.trim() || '',
      phoneb: formData.phoneb.trim() || '',
      serviceRange: formData.serviceRange,
      enabled: formData.enabled
    }
    
    // 验证经办人2电话（如果填写了经办人2）
    if (submitData.contactb && !submitData.phoneb) {
      showToast('请输入经办人2的电话')
      return
    }
    
    if (submitData.phoneb && !submitData.contactb) {
      showToast('请输入经办人2的姓名')
      return
    }
    
    await agencyApi.updateAgency(route.params.id, submitData)
    
    showSuccessToast('代理机构更新成功')
    
    // 返回详情页面
    router.back()
    
  } catch (error) {
    console.error('更新代理机构失败:', error)
    showToast('更新失败，请重试')
  } finally {
    submitting.value = false
  }
}

const onServiceRangeConfirm = ({ selectedOptions }) => {
  formData.serviceRange = selectedOptions[0]?.value || ''
  showServiceRangePicker.value = false
}

// 生命周期
onMounted(() => {
  fetchAgencyDetail()
})

onActivated(() => {
  fetchAgencyDetail()
})
</script>

<style lang="scss" scoped>
.agency-edit-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 100px;
  flex-shrink: 0;
}

:deep(.van-field__control) {
  text-align: right;
}
</style>

<route lang="json5">
{
  name: 'AgencyEdit',
  meta: {
    title: '编辑代理机构'
  }
}
</route>
