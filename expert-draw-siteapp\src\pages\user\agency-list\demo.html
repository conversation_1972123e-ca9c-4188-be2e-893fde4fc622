<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽取代理机构列表页面演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            background: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .demo-container {
            width: 375px;
            height: 667px;
            background: #f5f5f5;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
        }
        
        .demo-info {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px;
            border-radius: 6px;
            font-size: 11px;
            text-align: center;
            z-index: 10;
        }
        
        .search-section {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: white;
            border-bottom: 1px solid #f0f0f0;
            margin-top: 30px;
        }
        
        .search-input {
            flex: 1;
            height: 36px;
            background: #f7f8fa;
            border: none;
            border-radius: 18px;
            padding: 0 16px;
            font-size: 14px;
            color: #646566;
        }
        
        .filter-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-left: 12px;
            padding: 8px;
            cursor: pointer;
        }
        
        .filter-icon span {
            font-size: 12px;
            color: #646566;
            margin-top: 2px;
        }
        
        .status-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .tab-item {
            flex: 1;
            position: relative;
            padding: 16px 0;
            text-align: center;
            cursor: pointer;
        }
        
        .tab-item span {
            font-size: 16px;
            color: #646566;
            font-weight: 500;
        }
        
        .tab-item.active span {
            color: #07c160;
        }
        
        .tab-line {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: #07c160;
            border-radius: 2px;
        }
        
        .project-list {
            padding: 12px 16px;
            height: calc(100% - 200px);
            overflow-y: auto;
        }
        
        .project-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }
        
        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }
        
        .project-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #323233;
            line-height: 1.4;
            margin-right: 12px;
        }
        
        .project-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .project-status.normal {
            background: #f0f9ff;
            color: #1890ff;
        }
        
        .project-status.applied {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .project-info {
            margin-bottom: 16px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f7f8fa;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-row .label {
            font-size: 14px;
            color: #646566;
            min-width: 80px;
        }
        
        .info-row .value {
            font-size: 14px;
            color: #323233;
            text-align: right;
            flex: 1;
            margin-left: 12px;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
        }
        
        .action-btn {
            flex: 1;
            height: 36px;
            border-radius: 6px;
            font-size: 14px;
            border: none;
            cursor: pointer;
        }
        
        .action-btn.secondary {
            background: #f7f8fa;
            color: #646566;
            border: 1px solid #ebedf0;
        }
        
        .action-btn.primary {
            background: #07c160;
            color: white;
        }
        
        .action-btn.detail {
            background: #ff8c00;
            color: white;
        }
        
        .bottom-add-button {
            position: absolute;
            bottom: 20px;
            left: 16px;
            right: 16px;
        }
        
        .add-btn {
            width: 100%;
            height: 44px;
            background: #07c160;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .add-btn:hover {
            background: #06ad56;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-info">
            抽取代理机构列表页面演示 - 375x667 移动端视图
        </div>
        
        <!-- 搜索栏 -->
        <div class="search-section">
            <input type="text" class="search-input" placeholder="模糊检索项目名称">
            <div class="filter-icon">
                <div style="width: 18px; height: 18px; background: #646566; margin-bottom: 2px;"></div>
                <span>筛选</span>
            </div>
        </div>

        <!-- 状态标签 -->
        <div class="status-tabs">
            <div class="tab-item active">
                <span>正常</span>
                <div class="tab-line"></div>
            </div>
            <div class="tab-item">
                <span>已作废</span>
            </div>
        </div>

        <!-- 项目列表 -->
        <div class="project-list">
            <!-- 项目1 -->
            <div class="project-item">
                <div class="project-header">
                    <div class="project-title">这里是项目名称这里是项目</div>
                    <div class="project-status normal">未申请</div>
                </div>
                
                <div class="project-info">
                    <div class="info-row">
                        <span class="label">项目分类</span>
                        <span class="value">经办人</span>
                    </div>
                    <div class="info-row">
                        <span class="label">直属单位</span>
                        <span class="value">张三</span>
                    </div>
                    <div class="info-row">
                        <span class="label">承办处室</span>
                        <span class="value">经办时间</span>
                    </div>
                    <div class="info-row">
                        <span class="label">成都市农业农村局</span>
                        <span class="value">2025-12-12 12:22:22</span>
                    </div>
                    <div class="info-row">
                        <span class="label">采购品目</span>
                        <span class="value">采购内容（用途）</span>
                    </div>
                    <div class="info-row">
                        <span class="label">办公用品</span>
                        <span class="value">设备更新</span>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="action-btn secondary">作废</button>
                    <button class="action-btn primary">采购代理申请</button>
                </div>
            </div>

            <!-- 项目2 -->
            <div class="project-item">
                <div class="project-header">
                    <div class="project-title">这里是项目名称这里是项目</div>
                    <div class="project-status applied">申请详情</div>
                </div>
                
                <div class="project-info">
                    <div class="info-row">
                        <span class="label">项目分类</span>
                        <span class="value">经办人</span>
                    </div>
                    <div class="info-row">
                        <span class="label">直属单位</span>
                        <span class="value">张三</span>
                    </div>
                    <div class="info-row">
                        <span class="label">承办处室</span>
                        <span class="value">经办时间</span>
                    </div>
                    <div class="info-row">
                        <span class="label">成都市农业农村局</span>
                        <span class="value">2025-12-12 12:22:22</span>
                    </div>
                    <div class="info-row">
                        <span class="label">采购品目</span>
                        <span class="value">采购内容（用途）</span>
                    </div>
                    <div class="info-row">
                        <span class="label">办公用品</span>
                        <span class="value">设备更新</span>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="action-btn detail">申请详情</button>
                </div>
            </div>
        </div>

        <!-- 底部添加按钮 -->
        <div class="bottom-add-button">
            <button class="add-btn">
                <span>+</span>
                无预算项目前期工作抽取代理机构
            </button>
        </div>
    </div>
    
    <script>
        console.log('抽取代理机构列表页面演示加载完成');
        
        // 模拟交互
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const text = this.textContent;
                alert(`点击了：${text}`);
            });
        });
        
        document.querySelector('.add-btn').addEventListener('click', function() {
            alert('点击了：无预算项目前期工作抽取代理机构');
        });
        
        // 状态切换
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                const tabLine = this.querySelector('.tab-line');
                if (!tabLine) {
                    const line = document.createElement('div');
                    line.className = 'tab-line';
                    this.appendChild(line);
                }
            });
        });
    </script>
</body>
</html>
