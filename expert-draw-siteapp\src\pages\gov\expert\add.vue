<!-- 新增专家页面 -->
<template>
  <div class="expert-add-page">
    <van-form @submit="handleSubmit" ref="formRef">
      <van-cell-group inset title="基本信息">
        <van-field
          v-model="formData.name"
          name="name"
          label="专家姓名"
          placeholder="请输入专家姓名"
          :rules="[{ required: true, message: '请输入专家姓名' }]"
        />
        
        <van-field
          v-model="genderText"
          is-link
          readonly
          name="gender"
          label="性别"
          placeholder="请选择性别"
          @click="showGenderPicker = true"
          :rules="[{ required: true, message: '请选择性别' }]"
        />
        
        <van-field
          v-model="formData.birthday"
          is-link
          readonly
          name="birthday"
          label="出生年月"
          placeholder="请选择出生年月"
          @click="showDatePicker = true"
          :rules="[{ required: true, message: '请选择出生年月' }]"
        />
        
        <van-field
          v-model="formData.idNum"
          name="idNum"
          label="身份证号"
          placeholder="请输入身份证号"
          :rules="[
            { required: true, message: '请输入身份证号' },
            { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号' }
          ]"
        />
        
        <van-field
          v-model="formData.phone"
          name="phone"
          label="联系电话"
          placeholder="请输入联系电话"
          :rules="[
            { required: true, message: '请输入联系电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]"
        />
      </van-cell-group>

      <van-cell-group inset title="专业信息">
        <van-field
          v-model="typeText"
          is-link
          readonly
          name="type"
          label="专家类型"
          placeholder="请选择专家类型"
          @click="showTypePicker = true"
          :rules="[{ required: true, message: '请选择专家类型' }]"
        />
        
        <van-field
          v-model="formData.post"
          name="post"
          label="单位职务"
          placeholder="请输入单位职务"
          :rules="[{ required: true, message: '请输入单位职务' }]"
        />
        
        <van-field
          v-model="formData.field"
          name="field"
          label="所在领域"
          placeholder="请输入所在领域"
          :rules="[{ required: true, message: '请输入所在领域' }]"
        />
        
        <van-field
          v-model="formData.level"
          name="level"
          label="专业级别"
          placeholder="请输入专业级别"
          :rules="[{ required: true, message: '请输入专业级别' }]"
        />
        
        <van-field
          v-model="serviceRangeText"
          is-link
          readonly
          name="serviceRange"
          label="服务范围"
          placeholder="请选择服务范围"
          @click="showServiceRangePicker = true"
          :rules="[{ required: true, message: '请选择服务范围' }]"
        />
      </van-cell-group>
      
      <van-cell-group inset title="状态设置">
        <van-field name="enabled" label="启用状态">
          <template #input>
            <van-switch v-model="formData.enabled" />
          </template>
        </van-field>
      </van-cell-group>
      
      <div class="submit-section">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="submitting"
        >
          {{ submitting ? '提交中...' : '提交' }}
        </van-button>
      </div>
    </van-form>

    <!-- 性别选择器 -->
    <van-popup v-model:show="showGenderPicker" position="bottom">
      <van-picker
        :columns="genderOptions"
        @confirm="onGenderConfirm"
        @cancel="showGenderPicker = false"
      />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        :min-date="minDate"
        :max-date="maxDate"
      />
    </van-popup>

    <!-- 专家类型选择器 -->
    <van-popup v-model:show="showTypePicker" position="bottom">
      <van-picker
        :columns="typeOptions"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>

    <!-- 服务范围选择器 -->
    <van-popup v-model:show="showServiceRangePicker" position="bottom">
      <van-picker
        :columns="serviceRangeOptions"
        @confirm="onServiceRangeConfirm"
        @cancel="showServiceRangePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'
import { expertApi } from '@/api/expert'

const router = useRouter()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const showGenderPicker = ref(false)
const showDatePicker = ref(false)
const showTypePicker = ref(false)
const showServiceRangePicker = ref(false)
const currentDate = ref(new Date())
const typeOptions = ref([])

const formData = reactive({
  name: '',
  gender: '',
  birthday: '',
  idNum: '',
  phone: '',
  typeId: '',
  post: '',
  field: '',
  level: '',
  serviceRange: '',
  enabled: true
})

// 选项数据
const genderOptions = [
  { text: '男', value: 'MALE' },
  { text: '女', value: 'FEMALE' }
]

const serviceRangeOptions = [
  { text: '全部', value: 'ALL' },
  { text: '局机关和直属单位', value: 'DIRECTLY' },
  { text: '区县', value: 'DISTRICT' }
]

const minDate = new Date(1950, 0, 1)
const maxDate = new Date()

// 计算属性
const genderText = computed(() => {
  const option = genderOptions.find(item => item.value === formData.gender)
  return option ? option.text : ''
})

const typeText = computed(() => {
  const option = typeOptions.value.find(item => item.value === formData.typeId)
  return option ? option.text : ''
})

const serviceRangeText = computed(() => {
  const option = serviceRangeOptions.find(item => item.value === formData.serviceRange)
  return option ? option.text : ''
})

// 方法
const fetchExpertTypes = async () => {
  try {
    const response = await expertApi.getExpertCategories()
    typeOptions.value = response.list?.map(item => ({
      text: item.name,
      value: item.id
    })) || []
  } catch (error) {
    console.error('获取专家类型失败:', error)
    // 使用模拟数据
    typeOptions.value = [
      { text: '农业测试类', value: '1' },
      { text: '财务审计类', value: '2' }
    ]
  }
}

const onGenderConfirm = ({ selectedOptions }) => {
  formData.gender = selectedOptions[0].value
  showGenderPicker.value = false
}

const onDateConfirm = (value) => {
  const year = value.getFullYear()
  const month = String(value.getMonth() + 1).padStart(2, '0')
  const day = String(value.getDate()).padStart(2, '0')
  formData.birthday = `${year}-${month}-${day}`
  showDatePicker.value = false
}

const onTypeConfirm = ({ selectedOptions }) => {
  formData.typeId = selectedOptions[0].value
  showTypePicker.value = false
}

const onServiceRangeConfirm = ({ selectedOptions }) => {
  formData.serviceRange = selectedOptions[0].value
  showServiceRangePicker.value = false
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    const submitData = {
      name: formData.name.trim(),
      gender: formData.gender,
      birthday: formData.birthday,
      idNum: formData.idNum.trim(),
      phone: formData.phone.trim(),
      typeId: formData.typeId,
      post: formData.post.trim(),
      field: formData.field.trim(),
      level: formData.level.trim(),
      serviceRange: formData.serviceRange,
      enabled: formData.enabled
    }
    
    await expertApi.createExpert(submitData)
    
    showSuccessToast('专家添加成功')
    router.back()
    
  } catch (error) {
    console.error('添加专家失败:', error)
    showToast('添加失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchExpertTypes()
})
</script>

<style lang="scss" scoped>
.expert-add-page {
  min-height: 100vh;
  background-color: var(--van-background);
  padding: 16px;
}

.submit-section {
  margin-top: 32px;
  padding: 0 16px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-field__label) {
  width: 100px;
  flex-shrink: 0;
}

:deep(.van-field__control) {
  text-align: right;
}
</style>

<route lang="json5">
{
  name: 'ExpertAdd',
  meta: {
    title: '新增专家'
  }
}
</route>
