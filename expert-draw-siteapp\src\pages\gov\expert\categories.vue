<!-- 专家种类列表页面 -->
<template>
  <div class="expert-categories-page">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索检索名称、联系人、电话"
        @search="handleSearch"
        @clear="handleClear"
        show-action
      >
        <template #action>
          <div @click="handleSearch" class="search-action">筛选</div>
        </template>
      </van-search>
    </div>

    <!-- 专家种类列表 -->
    <div class="categories-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="category in categoriesList"
            :key="category.id"
            class="category-card"
            @click="handleCategoryDetail(category)"
          >
            <div class="category-header">
              <div class="category-name">{{ category.name }}</div>
              <div class="category-status" :class="getStatusClass(category.enabled)">
                {{ getStatusText(category.enabled) }}
              </div>
            </div>
            
            <div class="category-info">
              <div class="info-row">
                <div class="info-item">
                  <div class="info-label">人数</div>
                  <div class="info-value">{{ category.plNumber || 0 }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">数据创建时间</div>
                  <div class="info-value">{{ formatDateTime(category.createTime) }}</div>
                </div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && categoriesList.length === 0"
      description="暂无专家种类数据"
      image="search"
    />

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAddCategory"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { expertApi } from '@/api/expert'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const categoriesList = ref([])
const currentPage = ref(1)
const pageSize = ref(20)

// 方法
const fetchCategoriesList = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1
      finished.value = false
    }
    
    loading.value = true
    const params = {
      pageSize: pageSize.value.toString(),
      enabled: 'true' // 默认只显示启用的
    }
    
    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }
    
    const response = await expertApi.getExpertTypes(params)
    
    if (isRefresh) {
      categoriesList.value = response.list || response || []
    } else {
      categoriesList.value.push(...(response.list || response || []))
    }
    
    // 判断是否还有更多数据
    if (!response.list || response.list.length < pageSize.value) {
      finished.value = true
    }
    
  } catch (error) {
    console.error('获取专家种类列表失败:', error)
    showToast('获取数据失败')
    
    // 使用模拟数据
    if (isRefresh || categoriesList.value.length === 0) {
      categoriesList.value = [
        {
          id: '1',
          name: '财务审计类',
          description: '财务审计相关专家',
          plNumber: '59',
          enabled: 'true',
          createTime: '2025-12-12 12:00:23'
        },
        {
          id: '2',
          name: '农村金融保险类',
          description: '农村金融保险相关专家',
          plNumber: '59',
          enabled: 'false',
          createTime: '2025-12-12 12:00:23'
        },
        {
          id: '3',
          name: '农产品加工类',
          description: '农产品加工相关专家',
          plNumber: '59',
          enabled: 'false',
          createTime: '2025-12-12 12:00:23'
        }
      ]
      finished.value = true
    }
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onLoad = () => {
  if (!finished.value) {
    currentPage.value++
    fetchCategoriesList()
  }
}

const onRefresh = () => {
  fetchCategoriesList(true)
}

const handleSearch = () => {
  fetchCategoriesList(true)
}

const handleClear = () => {
  searchKeyword.value = ''
  fetchCategoriesList(true)
}

const handleCategoryDetail = (category) => {
  router.push(`/gov/expert/categories/detail/${category.id}`)
}

const handleAddCategory = () => {
  router.push('/gov/expert/categories/add')
}

const getStatusText = (enabled) => {
  return enabled === 'true' || enabled === true ? '已启用' : '已禁用'
}

const getStatusClass = (enabled) => {
  return enabled === 'true' || enabled === true ? 'enabled' : 'disabled'
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  // 如果已经是格式化的字符串，直接返回
  if (dateString.includes('-') && dateString.includes(':')) {
    return dateString
  }
  // 否则格式化日期
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}

// 生命周期
onMounted(() => {
  fetchCategoriesList(true)
})

onActivated(() => {
  fetchCategoriesList(true)
})
</script>

<style lang="scss" scoped>
.expert-categories-page {
  min-height: 100vh;
  background-color: var(--van-background);
}

.search-section {
  background: white;
  padding: 8px 0;
  border-bottom: 1px solid var(--van-border-color);
  
  .search-action {
    color: var(--van-text-color);
    font-size: 14px;
  }
}

.categories-list {
  padding: 8px 16px;
}

.category-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--van-text-color);
  flex: 1;
  margin-right: 12px;
}

.category-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  
  &.enabled {
    background-color: #E8F5E8;
    color: #4CAF50;
  }
  
  &.disabled {
    background-color: #FFF3E0;
    color: #FF9800;
  }
}

.category-info {
  .info-row {
    display: flex;
    justify-content: space-between;
  }

  .info-item {
    flex: 1;
    
    &:first-child {
      margin-right: 16px;
    }
  }

  .info-label {
    font-size: 12px;
    color: var(--van-text-color-2);
    margin-bottom: 4px;
  }

  .info-value {
    font-size: 14px;
    color: var(--van-text-color);
    font-weight: 500;
  }
}
</style>

<route lang="json5">
{
  name: 'ExpertCategoriesList',
  meta: {
    title: '专家种类列表'
  }
}
</route>
